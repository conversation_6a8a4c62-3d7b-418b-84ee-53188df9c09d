"""
This module provides functionalities for setting up a PostgreSQL database
and managing `questions` and `user_assessment` tables with synchronous operations.
"""

import datetime
import json
import os
from collections import OrderedDict

import psycopg2
import psycopg2.extras
from dotenv import load_dotenv

from ..config.db_config import DATABASE_CONFIG
from ..utils.logger import (
    debug,
    error,
    log_database_error,
    log_database_operation,
    warning,
)
from .skill_manager import valid_skill_description

load_dotenv()


def insert_question_data(data: list, skill_id: int = None):
    """
    Insert quiz question data into the `questions` table.
    Now accepts questions associated with any of the assessment's skills.

    Args:
        data (list): List of question data dictionaries to insert
        skill_id (int, optional): The skill ID to associate with these questions
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                for entry in data:
                    # Validate topic matches any skill description for this assessment
                    if not entry.get("Topic") or not valid_skill_description(entry["Topic"]):
                        raise ValueError("Topic must match a valid skill description")

                    # Include skill_id in the insert if provided
                    if skill_id is not None:
                        cur.execute(
                            """
                            INSERT INTO questions
                            (topic, level, question, options, answer, skill_id)
                            VALUES (%s, %s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                entry["Topic"],
                                entry["Level"],
                                entry["Question"],
                                json.dumps(entry["Options"]),
                                entry["Answer"],
                                skill_id,
                            ),
                        )
                    else:
                        # Try to find the skill_id based on the topic
                        cur.execute(
                            "SELECT id FROM skills WHERE name = %s OR name = SPLIT_PART(%s, '_', 1)",
                            (entry["Topic"], entry["Topic"]),
                        )
                        skill_row = cur.fetchone()
                        if skill_row:
                            found_skill_id = skill_row[0]
                            cur.execute(
                                """
                                INSERT INTO questions
                                (topic, level, question, options, answer, skill_id)
                                VALUES (%s, %s, %s, %s, %s, %s)
                                ON CONFLICT DO NOTHING
                                """,
                                (
                                    entry["Topic"],
                                    entry["Level"],
                                    entry["Question"],
                                    json.dumps(entry["Options"]),
                                    entry["Answer"],
                                    found_skill_id,
                                ),
                            )
                        else:
                            # If we can't find a skill_id, we can't insert the question
                            # due to the NOT NULL constraint
                            error(f"Cannot insert question without skill_id for topic: {entry['Topic']}")
                            raise ValueError(
                                f"No skill found for topic: {entry['Topic']}. Cannot insert question without skill_id."
                            )
                conn.commit()
                log_database_operation("insert", "questions", affected_rows=len(data))
    except Exception as e:
        log_database_error("insert", "questions", e)
        raise


# Fetch final question count from environment variables
FINAL_QUESTION_COUNT = int(os.getenv("FINAL_QUESTION_COUNT", "20"))


def divide_number(n):
    """
    Get question counts for each difficulty level from environment variables.
    This function no longer divides the number, but uses environment variables instead.
    The parameter n is kept for backward compatibility.
    """
    easy_count = int(os.getenv("EASY_QUESTIONS_COUNT", "10"))
    intermediate_count = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "10"))
    advanced_count = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "10"))

    return easy_count, intermediate_count, advanced_count


def insert_final_questions_db(question_ids):
    """
    Insert selected questions into the `final_questions` table only if there are
    enough questions in each category based on FINAL_QUESTION_COUNT.

    Args:
        question_ids (list): A list of question IDs to add to `final_questions`.

    Returns:
        dict: A result message indicating success or failure.
    """
    easy_count, intermediate_count, advanced_count = divide_number(FINAL_QUESTION_COUNT)

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First check if all question IDs exist
                cur.execute(
                    """
                    SELECT COUNT(*) FROM questions
                    WHERE que_id = ANY(%s)
                    """,
                    (question_ids,),
                )
                count = cur.fetchone()[0]
                if count != len(question_ids):
                    return {"error": "Some question IDs do not exist"}

                cur.execute(
                    """
                    SELECT level, COUNT(*) FROM questions
                    WHERE que_id = ANY(%s)
                    GROUP BY level;
                """,
                    (question_ids,),
                )

                level_counts = dict(cur.fetchall())

                selected_easy = level_counts.get("easy", 0)
                selected_intermediate = level_counts.get("intermediate", 0)
                selected_advanced = level_counts.get("advanced", 0)

                if (
                    selected_easy >= easy_count
                    and selected_intermediate >= intermediate_count
                    and selected_advanced >= advanced_count
                ):
                    cur.execute(
                        """
                        INSERT INTO final_questions
                        (que_id, topic, level, question, options, answer)
                        SELECT que_id, topic, level, question, options, answer FROM questions
                        WHERE que_id = ANY(%s)
                        ON CONFLICT DO NOTHING
                        RETURNING que_id;
                    """,
                        (question_ids,),
                    )

                    inserted_ids = [row[0] for row in cur.fetchall()]
                    conn.commit()

                    return {
                        "message": "Questions added to final_questions table.",
                        "inserted_ids": inserted_ids,
                    }

                return {
                    "error": (
                        "Selection must contain at least "
                        f"{easy_count} easy, {intermediate_count} intermediate, "
                        f"and {advanced_count} advanced questions. Found: "
                        f"easy: {selected_easy}, intermediate: {selected_intermediate}, "
                        f"advanced: {selected_advanced}"
                    )
                }
    except Exception as e:
        return {"error": f"Error inserting final questions: {str(e)}"}


def insert_user_data(data: dict):
    """
    Insert user quiz assessment data into the `user_assessment` table.

    Args:
        data (dict): A dictionary with the user's assessment data.

    Raises:
        ValueError: If `data` is not a dictionary.

    Notes:
        - Stores user-specific data like quiz type, answers, score, etc.
        - Options are stored as JSONB for efficient querying.
    """
    if not isinstance(data, dict):
        raise ValueError("`data` must be a dictionary.")

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    INSERT INTO user_assessment (user_id, topic, level,
                    quiz_type, que_id, question, options, correct_answer,
                    user_answer, result, score)
                    VALUES (%s, %s, %s, %s, %s, %s, %s, %s, %s, %s, %s)
                    """,
                    (
                        data["user_id"],
                        data["topic"],
                        data["level"],
                        data["quiz_type"],
                        data["que_id"],
                        data["question"],
                        json.dumps(data["options"]),
                        data["correct_answer"],
                        data["user_answer"],
                        data["result"],
                        data["score"],
                    ),
                )
                conn.commit()
    except Exception as e:
        log_database_error("insert", "user_assessment", e)


def fetch_questions(quiz_name):
    """
    Fetch all questions for a specific quiz from the `questions` table.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - The function fetches questions based on the `topic` (quiz_name).
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute("SELECT * FROM questions WHERE topic = %s;", (quiz_name,))
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        log_database_error("select", "questions", e, quiz_name=quiz_name)
        return []


def fetch_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True):
    """
    Fetch questions for specific skills from the `questions` table.

    Args:
        skill_ids (List[int]): List of skill IDs to fetch questions for.
        exclude_fixed_assessment_questions (bool): Whether to exclude questions
                                                  assigned to fixed assessments.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - For dynamic assessments, excludes questions assigned to fixed assessments by default.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    if not skill_ids:
        return []

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                placeholders = ",".join(["%s"] * len(skill_ids))

                if exclude_fixed_assessment_questions:
                    query = f"""SELECT q.* FROM questions q
                               WHERE q.skill_id IN ({placeholders})
                               AND q.que_id NOT IN (
                                   SELECT DISTINCT aq.question_id
                                   FROM assessment_questions aq
                                   JOIN assessments a ON aq.assessment_id = a.id
                                   WHERE a.question_selection_mode = 'fixed'
                               )"""
                else:
                    query = f"SELECT q.* FROM questions q WHERE q.skill_id IN ({placeholders})"

                cur.execute(query, skill_ids)
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        log_database_error("select", "questions", e, skill_ids=skill_ids)
        return []


def fetch_questions_for_fixed_assessment(assessment_id):
    """
    Fetch pre-selected questions for a fixed assessment from the `assessment_questions` table.

    Args:
        assessment_id (int): The ID of the assessment to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - Only fetches questions that are specifically assigned to the fixed assessment.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT q.*
                    FROM questions q
                    JOIN assessment_questions aq ON q.que_id = aq.question_id
                    WHERE aq.assessment_id = %s
                    """,
                    (assessment_id,),
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        log_database_error("select", "questions", e, assessment_id=assessment_id)
        return []


def fetch_dynamic_questions_excluding_fixed(quiz_name):
    """
    Fetch questions for dynamic assessments, excluding those assigned to fixed assessments.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.

    Notes:
        - Excludes questions that are assigned to any fixed assessment.
        - Uses `psycopg2.extras.DictCursor` to return results as dictionaries.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """SELECT q.* FROM questions q
                       WHERE q.topic = %s
                       AND q.que_id NOT IN (
                           SELECT DISTINCT aq.question_id
                           FROM assessment_questions aq
                           JOIN assessments a ON aq.assessment_id = a.id
                           WHERE a.question_selection_mode = 'fixed'
                       )""",
                    (quiz_name,),
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        log_database_error("select", "questions", e, quiz_name=quiz_name)
        return []


def count_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True):
    """
    Count questions available for specific skills.

    Args:
        skill_ids (List[int]): List of skill IDs to count questions for.
        exclude_fixed_assessment_questions (bool): Whether to exclude questions
                                                  assigned to fixed assessments.

    Returns:
        int: The count of available questions.

    Notes:
        - For dynamic assessments, excludes questions assigned to fixed assessments by default.
    """
    if not skill_ids:
        return 0

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                placeholders = ",".join(["%s"] * len(skill_ids))

                if exclude_fixed_assessment_questions:
                    query = f"""SELECT COUNT(*) FROM questions q
                               WHERE q.skill_id IN ({placeholders})
                               AND q.que_id NOT IN (
                                   SELECT DISTINCT aq.question_id
                                   FROM assessment_questions aq
                                   JOIN assessments a ON aq.assessment_id = a.id
                                   WHERE a.question_selection_mode = 'fixed'
                               )"""
                else:
                    query = f"SELECT COUNT(*) FROM questions q WHERE q.skill_id IN ({placeholders})"

                cur.execute(query, skill_ids)
                return cur.fetchone()[0]
    except Exception as e:
        log_database_error("count", "questions", e, skill_ids=skill_ids)
        return 0





def fetch_final_questions(quiz_name):
    """
    Fetch all questions that are part of fixed assessments for a specific quiz.

    This function has been updated to work with the current schema where questions
    are associated with assessments through the assessment_questions table.

    Args:
        quiz_name (str): The name of the quiz to fetch questions for.

    Returns:
        List[Dict]: A list of dictionaries, each representing a question and its attributes.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get questions that are part of fixed assessments related to this topic
                cur.execute(
                    """
                    SELECT q.*
                    FROM questions q
                    JOIN assessment_questions aq ON q.que_id = aq.question_id
                    JOIN assessments a ON aq.assessment_id = a.id
                    WHERE a.question_selection_mode = 'fixed' AND a.name LIKE %s
                    """,
                    (f"%{quiz_name}%",),
                )
                rows = cur.fetchall()
                return [dict(row) for row in rows]
    except Exception as e:
        error(f"Error fetching fixed assessment questions: {e}")
        return []


def fetch_user_progress(quiz_name, user_id, quiz_type):
    """
    Fetch the progress data of a specific user for a given quiz from the `user_assessment` table.

    Args:
        quiz_name (str): The name of the quiz (topic) to fetch progress for.
        user_id (str): The unique identifier of the user whose progress needs to be fetched.
        quiz_type (str): The type of the quiz
        (e.g., "practice", "assessment") to filter the results.

    Returns:
        List[Dict]: A list of dictionaries representing the user's progress, each containing:
            - `question`: The question text.
            - `options`: The available options for the question.
            - `correct_answer`: The correct answer for the question.
            - `user_answer`: The answer chosen by the user.
            - `result`: The result (e.g., "correct", "incorrect") for the question.
            - `score`: The score awarded for the question.

    Notes:
        - The `options` field is stored as JSONB in
          the database and is deserialized using `json.loads()`.
        - The returned list of progress entries is ordered for clarity.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:

                cur.execute(
                    """
                    SELECT que_id, question, options, correct_answer, user_answer, result, score
                    FROM user_assessment
                    WHERE topic = %s AND user_id = %s AND quiz_type = %s
                    ORDER BY id DESC;
                    """,
                    (quiz_name, user_id, quiz_type),
                )
                rows = cur.fetchall()
                debug(f"Fetched Rows: {rows}")
                expected_count = 10 if quiz_type.lower() == "mock" else 20

                seen_ids = set()
                latest_attempt = []

                for row in rows:
                    qid = row["que_id"]
                    if qid not in seen_ids:
                        seen_ids.add(qid)
                        latest_attempt.append(row)
                    if len(latest_attempt) == expected_count:
                        break

                if len(latest_attempt) < expected_count:
                    warning(f"Insufficient data: Expected {expected_count}, got {len(latest_attempt)}")
                    return []

                progress = []
                for row in reversed(latest_attempt):
                    options = json.loads(row["options"])
                    ordered_entry = OrderedDict(
                        [
                            ("question", row["question"]),
                            ("options", options),
                            ("correct_answer", row["correct_answer"]),
                            ("user_answer", row["user_answer"]),
                            ("result", row["result"]),
                            ("score", row["score"]),
                        ]
                    )
                    progress.append(ordered_entry)
                debug(f"Constructed Progress: {progress}")
                return progress

    except Exception as e:
        log_database_error(
            "select",
            "user_answers",
            e,
            quiz_name=quiz_name,
            user_id=user_id,
            quiz_type=quiz_type,
        )
        return []


def fetch_attempted_question_ids(quiz_name, user_id):
    """
    Fetch the IDs of questions that have already been attempted by a specific user.

    Args:
        quiz_name (str): The name of the quiz (topic) to filter the attempted questions.
        user_id (str): The unique identifier of
        the user whose attempted question IDs need to be fetched.

    Returns:
        Set[int]: A set of question IDs that have been attempted by the user in the given quiz.

    Notes:
        - The returned set ensures that each question ID is unique,
          as sets inherently avoid duplicates.
        - This function queries the `user_assessment` table
          to retrieve the question IDs (`que_id`) for the specified user and quiz.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT que_id FROM user_assessment WHERE topic = %s AND user_id = %s;",
                    (quiz_name, user_id),
                )
                rows = cur.fetchall()
                return {row[0] for row in rows}
    except Exception as e:
        log_database_error("select", "user_answers", e, quiz_name=quiz_name, user_id=user_id)
        return set()


def get_questions_by_level(quiz_name: str, level: str):
    """
    Retrieve questions from the `questions` table filtered by a specific level.

    Args:
        quiz_name (str): The name of the quiz (topic) for which to retrieve questions.
        level (str): The level of the questions
        to retrieve (e.g., "easy", "intermediate", "advanced").

    Returns:
        list: A list of questions (strings) for the specified level and topic.

    Raises:
        ValueError: If `level` is not a string.

    Notes:
        - This function queries the `questions` table,
          retrieving only the questions matching the given `quiz_name` and `level`.
        - If no questions are found or an error occurs, an empty list is returned.
    """
    if not isinstance(level, str):
        raise ValueError("`level` must be a string.")
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT question
                    FROM questions
                    WHERE topic = %s AND level = %s
                    """,
                    (quiz_name, level),
                )
                rows = cur.fetchall()

                questions = [row[0] for row in rows]
                return questions
    except Exception as e:
        log_database_error("select", "questions", e, quiz_name=quiz_name, level=level)
        return []


def get_final_question_ids(quiz_name: str):
    """
    Retrieve the question IDs for questions that are part of fixed assessments.

    This function has been updated to work with the current schema where questions
    are associated with assessments through the assessment_questions table.

    Args:
        quiz_name (str): The name of the quiz (topic) for which to retrieve question IDs.

    Returns:
        list: A list of question IDs for questions in fixed assessments.

    Raises:
        ValueError: If `quiz_name` is not a string.
    """
    if not isinstance(quiz_name, str):
        raise ValueError("`quiz_name` must be a string.")
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Get questions that are part of fixed assessments related to this topic
                cur.execute(
                    """
                    SELECT DISTINCT aq.question_id
                    FROM assessment_questions aq
                    JOIN assessments a ON aq.assessment_id = a.id
                    WHERE a.question_selection_mode = 'fixed' AND a.name LIKE %s
                    """,
                    (f"%{quiz_name}%",),
                )
                rows = cur.fetchall()
                question_ids = [row[0] for row in rows]
                return question_ids
    except Exception as e:
        error(f"Error retrieving fixed assessment question IDs: {e}")
        return []


def get_questions_for_check(quiz_name: str, question_id: str):
    """
    Retrieve a specific question from the `questions` table.

    Args:
        quiz_name (str): The name of the quiz (topic) to which the question belongs.
        question_id (str): The ID of the question to retrieve.

    Returns:
        dict: A dictionary containing the question data,
        including ID, text, options, answer, level, and topic.
        None: If no question is found or an error occurs.

    Raises:
        ValueError: If `question_id` cannot be converted to an integer.

    Notes:
        - The `question_id` should be a valid integer.
        - If the specified question doesn't exist, `None` will be returned.
    """
    try:

        question_id = int(question_id)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT que_id, question, options, answer, level, topic
                    FROM questions
                    WHERE topic = %s AND que_id = %s
                    """,
                    (quiz_name, question_id),
                )
                row = cur.fetchone()
                if row:
                    return {
                        "que_id": row[0],
                        "question": row[1],
                        "options": row[2],
                        "answer": row[3],
                        "level": row[4],
                        "topic": row[5],
                    }
                return None
    except ValueError:
        warning(f"Invalid question_id value: {question_id}")
        return None
    except Exception as e:
        log_database_error("select", "questions", e, question_id=question_id)
        return None


def get_performance_level(obtained_score, total_score):
    """
    Determine the performance level based on the obtained score percentage.

    Args:
        obtained_score (int): The score obtained by the user.
        total_score (int): The maximum possible score.

    Returns:
        str: The performance level (Fail, Basic, Acceptable, Exceed Expectation, OUTSTANDING).
    """
    if obtained_score < 0 or obtained_score > total_score:
        return "Invalid score"

    percentage = (obtained_score / total_score) * 100 if total_score > 0 else 0

    levels = [
        (0, "Fail"),
        (33, "Basic"),
        (62, "Acceptable"),
        (85, "Exceed Expectation"),
        (100, "OUTSTANDING"),
    ]

    # Corrected logic for level determination
    if percentage == 0 and obtained_score == 0:  # Handles the case where total_score might be 0 or obtained_score is 0
        return "Fail"

    performance = "Fail"  # Default
    for threshold, level in levels:
        if percentage >= threshold:  # Check if percentage is greater than or equal to threshold
            performance = level
        else:  # if it's less, then the previous level was correct
            break

    # Special case for 100%
    if percentage == 100:
        performance = "OUTSTANDING"

    return performance


def calculate_total_score(easy_attempted, intermediate_attempted, advanced_attempted):
    """
    Calculate total score based on attempted questions in different difficulty levels.

    Args:
        easy_attempted (int): Number of easy questions attempted.
        intermediate_attempted (int): Number of intermediate questions attempted.
        advanced_attempted (int): Number of advanced questions attempted.

    Returns:
        int: Total score based on the formula.
    """
    total_score = (easy_attempted * 1) + (intermediate_attempted * 2) + (advanced_attempted * 3)
    return total_score


def calculate_percentage(obtained, total, quiz_type):
    """Helper function to calculate the percentage only for 'final' quizzes."""
    if quiz_type == "final":
        return f"{round((obtained / total) * 100, 2)}%" if total > 0 else "0%"
    return ""








def assessment_report_by_topic(topic: str, quiz_type: str):
    """
    Retrieve user assessment data and optionally include score reports
    based on topic and quiz type.

    Args:
        topic (str): The topic to filter the assessment data.
        quiz_type (str): The type of quiz to filter the assessment data.

    Returns:
        dict: A dictionary containing 'base_report' (detailed assessments)
              and 'score_report' (aggregated user scores).

    Raises:
        ValueError: If the `quiz_type` is not valid.

    Notes:
        - The `topic` should match an existing topic in the database.
        - The `quiz_type` must match a valid quiz type ('mock' or 'final').
    """

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Query for base report (detailed assessments)
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id, question, options,
                        user_answer, correct_answer, result, score, time
                    FROM
                        user_assessment
                    WHERE
                        topic = %s AND quiz_type = %s
                    ORDER BY
                        user_id ASC, time ASC;
                    """,
                    (topic, quiz_type),
                )
                base_rows = cur.fetchall()
                base_report = (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in base_rows
                    ]
                    if base_rows
                    else []
                )

                # Query for score report (aggregated scores)
                cur.execute(
                    """
                    SELECT
                        user_id,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_assessment
                    WHERE
                        topic = %s AND quiz_type = %s
                    GROUP BY
                        user_id
                    ORDER BY
                        user_id ASC;
                    """,
                    (topic, quiz_type),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[2], row[4], row[6])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": topic,
                                "total_score": total_score,
                                "obtained_score": row[1],
                                "percentage": calculate_percentage(row[1], total_score, quiz_type),
                                "performance_level": get_performance_level(row[1], total_score),
                                "easy_attempted": row[2],
                                "easy_correct": row[3],
                                "intermediate_attempted": row[4],
                                "intermediate_correct": row[5],
                                "advanced_attempted": row[6],
                                "advanced_correct": row[7],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, topic=topic, quiz_type=quiz_type)
        return {"base_report": [], "score_report": []}


def get_session_and_assessment_details_by_code(session_code: str):
    """
    Retrieve session and associated assessment details for a given session code.

    Args:
        session_code (str): The session code to look up.

    Returns:
        dict: A dictionary with session_id, assessment_id, assessment_name,
              is_final, user_id, session_status, remaining_time_seconds,
              and attempted_questions if found.
        None: If the session code is not found or an error occurs.
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.user_id,
                        s.status AS session_status,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.is_final,
                        s.started_at,
                        s.completed_at,
                        a.duration_minutes
                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    WHERE s.code = %s;
                    """,
                    (session_code,),
                )
                result = cur.fetchone()
                if not result:
                    return None

                session_data = dict(result)

                # Calculate remaining time for sessions
                duration_minutes = session_data.get("duration_minutes", 30)
                total_duration_seconds = duration_minutes * 60

                if session_data["session_status"] == "in_progress" and session_data["started_at"]:
                    # Get current time in UTC
                    now = datetime.datetime.now(datetime.timezone.utc)

                    # Convert started_at to UTC if it's not already
                    started_at = session_data["started_at"]
                    if started_at.tzinfo is None:
                        started_at = started_at.replace(tzinfo=datetime.timezone.utc)

                    # Calculate elapsed time since session started
                    elapsed_time = now - started_at
                    elapsed_seconds = int(elapsed_time.total_seconds())

                    # Calculate actual active time (all elapsed time since no pause functionality)
                    active_seconds = elapsed_seconds

                    # Calculate remaining time
                    remaining_seconds = max(0, total_duration_seconds - active_seconds)

                    session_data["remaining_time_seconds"] = remaining_seconds
                else:
                    # For non-in-progress sessions, set default duration
                    session_data["remaining_time_seconds"] = total_duration_seconds

                # Get attempted questions for this session
                cur.execute(
                    """
                    SELECT question_id
                    FROM user_answers
                    WHERE session_id = %s
                    """,
                    (session_data["session_id"],),
                )
                attempted_questions = [row[0] for row in cur.fetchall()]
                session_data["attempted_questions"] = attempted_questions

                return session_data
    except Exception as e:
        log_database_error("select", "sessions", e, session_code=session_code)
        return None


def base_report_assessment_by_user(user_id: str, quiz_type: str):
    """
    Retrieve user assessment data based on the user_id and quiz type.

    Args:
        user_id (str): The user ID to filter the assessment data.
        quiz_type (str): The type of quiz to filter the assessment data by.

    Returns:
        list: A list of dictionaries containing the assessment data,
        including user answers, correct answers, and results.

    Notes:
        - The `user_id` should be a valid user ID present in the database.
        - The `quiz_type` must match a valid quiz type in the database.
    """
    try:

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT
                        id, user_id, topic, level, quiz_type, que_id,
                        question, options, user_answer, correct_answer,
                        result, score, time
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                        AND quiz_type = %s
                    ORDER BY
                        topic ASC, user_id ASC, time ASC;
                    """,
                    (user_id, quiz_type),
                )
                rows = cur.fetchall()
                return (
                    [
                        {
                            "id": row[0],
                            "user_id": row[1],
                            "topic": row[2],
                            "level": row[3],
                            "quiz_type": row[4],
                            "que_id": row[5],
                            "question": row[6],
                            "options": row[7],
                            "user_answer": row[8],
                            "correct_answer": row[9],
                            "result": row[10],
                            "score": row[11],
                            "time": row[12],
                        }
                        for row in rows
                    ]
                    if rows
                    else []
                )
    except Exception as e:
        log_database_error("select", "user_assessment", e)
        return []


def score_report_by_user(user_id: str, quiz_type: str):
    """
    Retrieve user assessment data based on the user's ID, including total score,
    performance level, and counts of easy, intermediate, and advanced questions answered correctly.

    Args:
        user_id (str): The user ID to filter the data.
        quiz_type (str): The type of quiz to filter the data ('mock' or 'final').

    Returns:
        list: List of dictionaries containing assessment data.

    Notes:
        - The `quiz_type` must be 'mock' or 'final'.
    """

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    """
                    SELECT
                        user_id,
                        topic,
                        SUM(score) AS obtained_score,
                        SUM(CASE WHEN level = 'easy' THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN level = 'advanced' THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_assessment
                    WHERE
                        user_id = %s
                        AND quiz_type = %s
                    GROUP BY
                        user_id, topic
                    ORDER BY
                        topic
                    """,
                    (user_id, quiz_type),
                )
                user_scores = cur.fetchall()

                if not user_scores:
                    return f"No data found for user: {user_id} with quiz type: {quiz_type}"

                results = [
                    {
                        "user_id": row[0],
                        "topic": row[1],
                        "total_score": calculate_total_score(row[3], row[5], row[7]),
                        "obtained_score": row[2],
                        "performance_level": get_performance_level(
                            row[2], calculate_total_score(row[3], row[5], row[7])
                        ),
                        "easy_attempted": row[3],
                        "easy_correct": row[4],
                        "intermediate_attempted": row[5],
                        "intermediate_correct": row[6],
                        "advanced_attempted": row[7],
                        "advanced_correct": row[8],
                    }
                    for row in user_scores
                ]

                return results

    except psycopg2.Error as e:
        log_database_error("select", "user_assessment", e, user_id=user_id, quiz_type=quiz_type)
        return []
    except Exception as e:
        error(
            "Unexpected error in score_report_by_user",
            exception=e,
            user_id=user_id,
            quiz_type=quiz_type,
        )
        return []


def generate_report_by_criteria(entry_date=None, user_id=None, topic=None, quiz_type=None):
    """
    Generate a report based on the provided date, user_id, topic, and quiz type.

    Args:
        entry_date (str, optional): The date for which the report is to be generated.
        user_id (str, optional): The user ID for which the report is to be generated.
        topic (str, optional): The topic for which the report is to be generated.
        quiz_type (str): The type of report (e.g., 'mock', 'final').

    Returns:
        tuple: Two lists containing the base report and score report data.
    """
    base_query = """
        SELECT
            id, user_id, topic, level, quiz_type, que_id, question, options,
            user_answer, correct_answer, result, score, time
        FROM
            user_assessment
        WHERE
            quiz_type = %s
    """
    score_query = """
        SELECT
            user_id, topic, SUM(score) AS obtained_score,
            SUM(CASE WHEN level = 'easy' THEN 1 ELSE 0 END) AS easy_attempted,
            SUM(CASE WHEN result = 'Correct' AND level = 'easy' THEN 1 ELSE 0 END) AS easy_correct,
            SUM(CASE WHEN level = 'intermediate' THEN 1 ELSE 0 END) AS intermediate_attempted,
            SUM(CASE WHEN result = 'Correct' AND level = 'intermediate'
            THEN 1 ELSE 0 END) AS intermediate_correct,
            SUM(CASE WHEN level = 'advanced' THEN 1 ELSE 0 END) AS advanced_attempted,
            SUM(CASE WHEN result = 'Correct' AND level = 'advanced'
            THEN 1 ELSE 0 END) AS advanced_correct
        FROM
            user_assessment
        WHERE
            quiz_type = %s
        GROUP BY
            user_id, topic
    """

    conditions = []
    params = [quiz_type]

    if entry_date:
        conditions.append("DATE(time) = TO_DATE(%s, 'DD-MM-YYYY')::DATE")
        params.append(entry_date)
    if user_id:
        conditions.append("user_id = %s")
        params.append(user_id)
    if topic:
        conditions.append("topic = %s")
        params.append(topic)

    if conditions:
        condition_str = " AND " + " AND ".join(conditions)
        base_query += condition_str
        score_query += condition_str

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(base_query, params)
                base_report = [
                    {
                        "id": row[0],
                        "user_id": row[1],
                        "topic": row[2],
                        "level": row[3],
                        "quiz_type": row[4],
                        "que_id": row[5],
                        "question": row[6],
                        "options": row[7],
                        "user_answer": row[8],
                        "correct_answer": row[9],
                        "result": row[10],
                        "score": row[11],
                        "time": row[12],
                    }
                    for row in cur.fetchall()
                ]

                cur.execute(score_query, params)
                score_report = [
                    {
                        "user_id": row[0],
                        "topic": row[1],
                        "total_score": calculate_total_score(row[3], row[5], row[7]),
                        "obtained_score": row[2],
                        "performance_level": get_performance_level(
                            row[2], calculate_total_score(row[3], row[5], row[7])
                        ),
                        "easy_attempted": row[3],
                        "easy_correct": row[4],
                        "intermediate_attempted": row[5],
                        "intermediate_correct": row[6],
                        "advanced_attempted": row[7],
                        "advanced_correct": row[8],
                    }
                    for row in cur.fetchall()
                ]

                return base_report, score_report

    except Exception as e:
        log_database_error(
            "select",
            "user_assessment",
            e,
            entry_date=entry_date,
            user_id=user_id,
            topic=topic,
            quiz_type=quiz_type,
        )
        return [], []


def insert_quiz_creation_logs(data: list):
    """
    Insert quiz creation details into the `quiz_creation_logs` table.

    Args:
        data (list): A list of dictionaries, each containing:
            - "user_id" (str): The admin's username.
            - "assessment_description" (str): The assessment description.
            - "assessment_name" (str): The assessment name.
            - "total_questions" (int): The total number of questions in the quiz.
            - "assessment_id" (int): The ID of the created assessment.

    Raises:
        ValueError: If `data` is not a list of dictionaries.

    Notes:
        Uses the updated column names after migration 025.
    """
    if not isinstance(data, list) or not all(isinstance(entry, dict) for entry in data):
        raise ValueError("`data` must be a list of dictionaries.")

    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                for entry in data:
                    # Support both old and new column names for backward compatibility
                    assessment_name = entry.get("assessment_name") or entry.get("quiz_name")
                    assessment_description = entry.get("assessment_description") or entry.get("topic")
                    assessment_id = entry.get("assessment_id")

                    # Truncate values to fit VARCHAR(255) constraints
                    user_id = entry["user_id"][:255] if len(entry["user_id"]) > 255 else entry["user_id"]
                    truncated_assessment_name = (
                        assessment_name[:255] if assessment_name and len(assessment_name) > 255 else assessment_name
                    )
                    truncated_assessment_description = (
                        assessment_description[:255]
                        if assessment_description and len(assessment_description) > 255
                        else assessment_description
                    )

                    if assessment_id:
                        # New format with assessment_id
                        cur.execute(
                            """
                            INSERT INTO quiz_creation_logs
                            (user_id, assessment_name, assessment_description, total_questions, assessment_id)
                            VALUES (%s, %s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                user_id,
                                truncated_assessment_name,
                                truncated_assessment_description,
                                entry["total_questions"],
                                assessment_id,
                            ),
                        )
                    else:
                        # Legacy format without assessment_id
                        cur.execute(
                            """
                            INSERT INTO quiz_creation_logs
                            (user_id, assessment_name, assessment_description, total_questions)
                            VALUES (%s, %s, %s, %s)
                            ON CONFLICT DO NOTHING
                            """,
                            (
                                user_id,
                                truncated_assessment_name,
                                truncated_assessment_description,
                                entry["total_questions"],
                            ),
                        )
                conn.commit()
    except psycopg2.DatabaseError as db_error:
        log_database_error("insert", "quiz_creation_logs", db_error)
    except ValueError as val_error:
        error("Value error during quiz creation log insertion", exception=val_error)
    except Exception as e:
        log_database_error("insert", "quiz_creation_logs", e)


def get_assessment_by_id(assessment_id: int, include_questions: bool = True, include_answers: bool = False):
    """
    Get a single assessment with its questions and associated skills.

    Args:
        assessment_id: The ID of the assessment to retrieve
        include_questions: Whether to include available_questions in the response
        include_answers: Whether to include answers in the response (for security)

    Returns:
        A dictionary containing assessment details, or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes,
                           created_at, updated_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return None

                assessment_dict = dict(assessment)

                # Get the skill IDs associated with this assessment
                cur.execute(
                    "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                    (assessment_id,),
                )
                results = cur.fetchall()
                skill_ids = []
                if results:
                    # Extract skill IDs from the results
                    # Handle both individual skill IDs and JSON arrays
                    for row in results:
                        skill_id = row[0]
                        # Check if the skill_id is a JSON string
                        if isinstance(skill_id, str) and skill_id.startswith("[") and skill_id.endswith("]"):
                            try:
                                # Parse the JSON string into a list of skill IDs
                                json_skill_ids = json.loads(skill_id)
                                skill_ids.extend(json_skill_ids)
                            except json.JSONDecodeError:
                                # If it's not valid JSON, treat it as a single skill ID
                                skill_ids.append(skill_id)
                        else:
                            # It's a single skill ID
                            skill_ids.append(skill_id)

                    if skill_ids:
                        # Get skill details using parameterized query
                        placeholders = ",".join(["%s"] * len(skill_ids))
                        query = f"""
                            SELECT id, name, description
                            FROM skills
                            WHERE id IN ({placeholders})
                        """
                        cur.execute(query, skill_ids)
                    skills = [dict(row) for row in cur.fetchall()]
                    assessment_dict["skills"] = skills
                else:
                    assessment_dict["skills"] = []

                # If this is a fixed assessment, get the questions only if requested
                if assessment["question_selection_mode"] == "fixed" and include_questions:
                    # Get all questions for this assessment
                    if include_answers:
                        answer_column = "q.answer"
                    else:
                        answer_column = "NULL as answer"

                    cur.execute(
                        f"""
                        SELECT q.que_id, q.topic, q.level, q.question, q.options, {answer_column}, q.time, q.skill_id,
                               s.name as skill_name
                        FROM questions q
                        JOIN assessment_questions aq ON q.que_id = aq.question_id
                        JOIN skills s ON q.skill_id = s.id
                        WHERE aq.assessment_id = %s
                        ORDER BY q.level, q.time DESC
                        """,
                        (assessment_id,),
                    )
                    selected_questions = [dict(row) for row in cur.fetchall()]
                    assessment_dict["selected_questions"] = selected_questions

                    # Get counts by difficulty
                    easy_count = sum(1 for q in selected_questions if q["level"] == "easy")
                    intermediate_count = sum(1 for q in selected_questions if q["level"] == "intermediate")
                    advanced_count = sum(1 for q in selected_questions if q["level"] == "advanced")

                    assessment_dict["question_counts"] = {
                        "easy": easy_count,
                        "intermediate": intermediate_count,
                        "advanced": advanced_count,
                        "total": len(selected_questions),
                    }
                elif assessment["question_selection_mode"] == "fixed":
                    # Still get question counts even when not including questions
                    cur.execute(
                        """
                        SELECT q.level, COUNT(*) as count
                        FROM questions q
                        JOIN assessment_questions aq ON q.que_id = aq.question_id
                        WHERE aq.assessment_id = %s
                        GROUP BY q.level
                        """,
                        (assessment_id,),
                    )
                    count_results = cur.fetchall()

                    # Initialize counts
                    easy_count = 0
                    intermediate_count = 0
                    advanced_count = 0

                    # Populate counts from results
                    for row in count_results:
                        level = row[0]
                        count = row[1]
                        if level == "easy":
                            easy_count = count
                        elif level == "intermediate":
                            intermediate_count = count
                        elif level == "advanced":
                            advanced_count = count

                    assessment_dict["question_counts"] = {
                        "easy": easy_count,
                        "intermediate": intermediate_count,
                        "advanced": advanced_count,
                        "total": easy_count + intermediate_count + advanced_count,
                    }

                # If there are skill IDs, get all available questions for these skills
                if skill_ids:
                    # Use parameterized query with placeholders for each skill ID
                    placeholders = ",".join(["%s"] * len(skill_ids))

                    if include_answers:
                        answer_column = "q.answer"
                    else:
                        answer_column = "NULL as answer"

                    query = f"""
                        SELECT q.que_id, q.topic, q.level, q.question, q.options, {answer_column}, q.time, q.skill_id,
                               s.name as skill_name,
                               CASE WHEN aq.question_id IS NOT NULL THEN true ELSE false END as selected
                        FROM questions q
                        JOIN skills s ON q.skill_id = s.id
                        LEFT JOIN assessment_questions aq ON q.que_id = aq.question_id AND aq.assessment_id = %s
                        WHERE q.skill_id IN ({placeholders})
                        ORDER BY q.level, q.time DESC
                    """
                    # First parameter is assessment_id, followed by all skill_ids
                    params = [assessment_id] + skill_ids
                    cur.execute(query, params)

                    all_questions = [dict(row) for row in cur.fetchall()]

                    # Only include questions in response if requested
                    if include_questions:
                        assessment_dict["available_questions"] = all_questions

                    # Get counts by difficulty
                    easy_count = sum(1 for q in all_questions if q["level"] == "easy")
                    intermediate_count = sum(1 for q in all_questions if q["level"] == "intermediate")
                    advanced_count = sum(1 for q in all_questions if q["level"] == "advanced")

                    # Get counts of already selected questions
                    selected_easy = sum(1 for q in all_questions if q["level"] == "easy" and q["selected"])
                    selected_intermediate = sum(
                        1 for q in all_questions if q["level"] == "intermediate" and q["selected"]
                    )
                    selected_advanced = sum(1 for q in all_questions if q["level"] == "advanced" and q["selected"])

                    # Get required counts from environment variables
                    required_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                    required_intermediate = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
                    required_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                    assessment_dict["question_stats"] = {
                        "available": {
                            "easy": easy_count,
                            "intermediate": intermediate_count,
                            "advanced": advanced_count,
                            "total": len(all_questions),
                        },
                        "selected": {
                            "easy": selected_easy,
                            "intermediate": selected_intermediate,
                            "advanced": selected_advanced,
                            "total": selected_easy + selected_intermediate + selected_advanced,
                        },
                        "required": {
                            "easy": required_easy,
                            "intermediate": required_intermediate,
                            "advanced": required_advanced,
                            "total": required_easy + required_intermediate + required_advanced,
                        },
                    }

                return assessment_dict

    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return None


def get_assessment_questions_by_id(assessment_id: int):
    """
    Get all available questions for an assessment based on its associated skills.

    Args:
        assessment_id: The ID of the assessment to retrieve questions for

    Returns:
        A dictionary containing assessment questions and related information, or None if not found
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists
                cur.execute(
                    "SELECT id, name, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()
                if not assessment:
                    return None

                # Get the skill IDs associated with this assessment
                cur.execute(
                    "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                    (assessment_id,),
                )
                results = cur.fetchall()
                if not results:
                    return {
                        "assessment_id": assessment_id,
                        "assessment_name": assessment["name"],
                        "questions": [],
                        "message": "No skills associated with this assessment",
                    }

                # Extract skill IDs from the results
                # Handle both individual skill IDs and JSON arrays
                skill_ids = []
                for row in results:
                    skill_id = row[0]
                    # Check if the skill_id is a JSON string
                    if isinstance(skill_id, str) and skill_id.startswith("[") and skill_id.endswith("]"):
                        try:
                            # Parse the JSON string into a list of skill IDs
                            json_skill_ids = json.loads(skill_id)
                            skill_ids.extend(json_skill_ids)
                        except json.JSONDecodeError:
                            # If it's not valid JSON, treat it as a single skill ID
                            skill_ids.append(skill_id)
                    else:
                        # It's a single skill ID
                        skill_ids.append(skill_id)

                # Get all questions for these skills
                # Use parameterized query with placeholders for each skill ID
                placeholders = ",".join(["%s"] * len(skill_ids))
                query = f"""
                    SELECT q.que_id, q.topic, q.level, q.question, q.options, q.answer, q.time, q.skill_id,
                           s.name as skill_name,
                           CASE WHEN aq.question_id IS NOT NULL THEN true ELSE false END as selected
                    FROM questions q
                    JOIN skills s ON q.skill_id = s.id
                    LEFT JOIN assessment_questions aq ON q.que_id = aq.question_id AND aq.assessment_id = %s
                    WHERE q.skill_id IN ({placeholders})
                    ORDER BY q.level, q.time DESC
                """
                # First parameter is assessment_id, followed by all skill_ids
                params = [assessment_id] + skill_ids
                cur.execute(query, params)

                questions = [dict(row) for row in cur.fetchall()]

                # Get counts by difficulty
                easy_count = sum(1 for q in questions if q["level"] == "easy")
                intermediate_count = sum(1 for q in questions if q["level"] == "intermediate")
                advanced_count = sum(1 for q in questions if q["level"] == "advanced")

                # Get counts of already selected questions
                selected_easy = sum(1 for q in questions if q["level"] == "easy" and q["selected"])
                selected_intermediate = sum(1 for q in questions if q["level"] == "intermediate" and q["selected"])
                selected_advanced = sum(1 for q in questions if q["level"] == "advanced" and q["selected"])

                # Get required counts from environment variables
                required_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                required_intermediate = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
                required_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                return {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment["name"],
                    "question_selection_mode": assessment["question_selection_mode"],
                    "questions": questions,
                    "counts": {
                        "easy": easy_count,
                        "intermediate": intermediate_count,
                        "advanced": advanced_count,
                        "selected_easy": selected_easy,
                        "selected_intermediate": selected_intermediate,
                        "selected_advanced": selected_advanced,
                        "required_easy": required_easy,
                        "required_intermediate": required_intermediate,
                        "required_advanced": required_advanced,
                    },
                }

    except Exception as e:
        error(f"Error fetching questions for assessment {assessment_id}: {str(e)}")


def assessment_report_with_question_stats(assessment_base_name: str, quiz_type: str):
    """
    Retrieve assessment data with question-wise statistics including user attendance.

    Args:
        assessment_base_name (str): The base name of the assessment (e.g., "data engineer_22_05_2025").
        quiz_type (str): The type of quiz to filter the assessment data (not used in new structure).

    Returns:
        dict: A dictionary containing 'base_report' (question-wise stats)
              and 'score_report' (aggregated user scores).
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Find the assessment by name pattern
                cur.execute(
                    """
                    SELECT id, name, question_selection_mode
                    FROM assessments
                    WHERE name LIKE %s
                """,
                    (f"{assessment_base_name}%",),
                )
                assessments = cur.fetchall()

                if not assessments:
                    warning(f"No assessments found for pattern '{assessment_base_name}%'")
                    return {"base_report": [], "score_report": []}

                # Use the first matching assessment
                assessment_id = assessments[0][0]
                assessment_name = assessments[0][1]

                # Query for question-wise statistics using the new structure
                # Get all questions that were actually used in this assessment
                cur.execute(
                    """
                    SELECT
                        q.que_id,
                        q.skill_id,
                        s.name as skill_name,
                        q.level,
                        q.question,
                        q.options,
                        q.answer,
                        q.topic as question_topic,
                        COUNT(DISTINCT sess.user_id) as total_attended_users,
                        COUNT(CASE WHEN ua.is_correct = true THEN 1 END) as correct_answers,
                        COUNT(ua.session_id) as total_attempts,
                        ROUND(
                            (COUNT(CASE WHEN ua.is_correct = true THEN 1 END) * 100.0 /
                             NULLIF(COUNT(ua.session_id), 0)), 2
                        ) as correct_percentage
                    FROM questions q
                    LEFT JOIN skills s ON q.skill_id = s.id
                    INNER JOIN user_answers ua ON q.que_id = ua.question_id
                    INNER JOIN sessions sess ON ua.session_id = sess.id AND sess.assessment_id = %s
                    GROUP BY q.que_id, q.skill_id, s.name, q.level, q.question, q.options, q.answer, q.topic
                    ORDER BY s.name, q.level, q.question
                    """,
                    (assessment_id,),
                )
                question_rows = cur.fetchall()

                base_report = []
                if question_rows:
                    for row in question_rows:
                        base_report.append(
                            {
                                "que_id": row[0],
                                "skill_name": row[2] or "Unknown",
                                "level": row[3],
                                "question": row[4],
                                "options": row[5],
                                "correct_answer": row[6],
                                "question_topic": row[7],
                                "total_attended_users": row[8],
                                "correct_answers": row[9],
                                "total_attempts": row[10],
                                "correct_percentage": row[11] or 0.0,
                            }
                        )

                # Query for score report (aggregated scores) - using new structure
                cur.execute(
                    """
                    SELECT
                        u.external_id as user_id,
                        SUM(ua.score) AS obtained_score,
                        SUM(CASE WHEN q.level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_attempted,
                        SUM(CASE WHEN ua.is_correct = true AND q.level = 'easy'
                        THEN 1 ELSE 0 END) AS easy_correct,
                        SUM(CASE WHEN q.level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_attempted,
                        SUM(CASE WHEN ua.is_correct = true AND q.level = 'intermediate'
                        THEN 1 ELSE 0 END) AS intermediate_correct,
                        SUM(CASE WHEN q.level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_attempted,
                        SUM(CASE WHEN ua.is_correct = true AND q.level = 'advanced'
                        THEN 1 ELSE 0 END) AS advanced_correct
                    FROM
                        user_answers ua
                        JOIN sessions sess ON ua.session_id = sess.id
                        JOIN users u ON sess.user_id = u.id
                        JOIN questions q ON ua.question_id = q.que_id
                    WHERE
                        sess.assessment_id = %s
                    GROUP BY
                        u.external_id
                    ORDER BY
                        u.external_id ASC;
                    """,
                    (assessment_id,),
                )
                score_rows = cur.fetchall()

                score_report = []
                if score_rows:
                    for row in score_rows:
                        total_score = calculate_total_score(row[2], row[4], row[6])

                        score_report.append(
                            {
                                "user_id": row[0],
                                "topic": assessment_name,
                                "total_score": total_score,
                                "obtained_score": row[1],
                                "percentage": calculate_percentage(row[1], total_score, quiz_type),
                                "performance_level": get_performance_level(row[1], total_score),
                                "easy_attempted": row[2],
                                "easy_correct": row[3],
                                "intermediate_attempted": row[4],
                                "intermediate_correct": row[5],
                                "advanced_attempted": row[6],
                                "advanced_correct": row[7],
                            }
                        )

                return {"base_report": base_report, "score_report": score_report}

    except Exception as e:
        log_database_error("select", "user_assessment", e, assessment_base_name=assessment_base_name)
        return {"base_report": [], "score_report": []}
