"""
FastAPI application for quiz/assessment management system.
"""

import asyncio
import json
import logging
import os
import random
import time
import uuid
from datetime import datetime
from typing import List, Optional

import httpx
import psycopg2
import psycopg2.extras
import uvicorn
from fastapi import APIRouter, Depends, FastAPI, HTTPException, Query, Request, status
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from pydantic import BaseModel, ValidationError
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.middleware.sessions import SessionMiddleware

from .api.middlewares.hashid_middleware import hash_ids_in_response
from .api.routes.auth_routes import setup_auth
from .api.routes.skill_routes import QuestionGenerationTaskRequest, skill_router
from .config.db_config import DATABASE_CONFIG, get_connection_pool
from .config.env_validator import check_environment
from .models.db_manager import (
    assessment_report_by_topic,
    assessment_report_by_user,
    assessment_report_with_question_stats,
    calculate_total_score_for_assessment,
    count_questions_for_skills,
    divide_number,
    fetch_attempted_question_ids,
    fetch_dynamic_questions_excluding_fixed,
    fetch_final_questions,
    fetch_questions_for_fixed_assessment,
    fetch_questions_for_skills,
    fetch_user_progress,
    get_assessment_by_id,
    get_assessment_description,
    get_assessment_questions_by_id,
    get_final_question_ids,
    get_performance_level_with_correct_total,
    get_questions_for_check,
    get_session_and_assessment_details_by_code,
    insert_final_questions_db,
    insert_quiz_creation_logs,
    insert_user_data,
)
from .utils.api_response import (
    error_response,
    paginated_response,
    raise_http_exception,
    success_response,
)
from .utils.db_utils import (
    execute_query,
    get_single_row,
    get_single_value,
    safe_json_dumps,
    safe_json_loads,
)
from .utils.hashid_utils import (
    decode_assessment_id,
    decode_session_code,
    decode_skill_id,
    detect_hash_type,
    encode_session_code,
)
from .utils.logger import (
    clear_context,
    debug,
    error,
    info,
    log_api_request,
    log_api_response,
    set_context,
    warning,
)
from .utils.performance_utils import (
    calculate_question_score,
)
from .utils.performance_utils import get_performance_level as get_performance_level_util
from .utils.session_utils import (
    get_session_details,
    get_session_score,
    get_session_user_id,
    validate_session_code_format,
)

# All validation_utils imports are unused and removed

# Dapr configuration
DAPR_HTTP_PORT = os.getenv("DAPR_HTTP_PORT_BACKEND", "3500")
DAPR_BASE_URL = f"http://localhost:{DAPR_HTTP_PORT}"
PUBSUB_NAME = "pubsub"
QUESTION_TASKS_TOPIC = "question-tasks"

# Create FastAPI app with metadata
app = FastAPI(
    title="Quiz API",
    description="API for quiz management and assessment",
    version="1.0.0",
    openapi_version="3.1.0",
)

# Create API router with /api prefix
api_router = APIRouter(prefix="/api")


# Add global exception handler for validation errors
@app.exception_handler(ValidationError)
async def validation_exception_handler(request: Request, exc: ValidationError):
    """Handle Pydantic validation errors with detailed error messages."""
    return JSONResponse(
        status_code=400,
        content={
            "detail": "Invalid request format",
            "errors": exc.errors(),
            "message": "Please check your request parameters and try again.",
        },
    )


# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    # Specify the frontend origin explicitly
    allow_origins=[os.getenv("FRONTEND_URL")] if os.getenv("FRONTEND_URL") else ["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


# Logging middleware
class LoggingMiddleware(BaseHTTPMiddleware):
    async def dispatch(self, request: Request, call_next):
        # Generate request ID
        request_id = str(uuid.uuid4())
        start_time = time.time()

        # Get user info from session if available
        user_id = None
        try:
            if hasattr(request, "session") and "session" in request.scope:
                user_info = request.session.get("user", {})
                user_id = user_info.get("sub") or user_info.get("email")
        except (AttributeError, AssertionError):
            # Session not available, continue without user_id
            pass

        # Log request
        log_api_request(
            method=request.method,
            endpoint=str(request.url.path),
            user_id=user_id,
            request_id=request_id,
            query_params=dict(request.query_params) if request.query_params else None,
        )

        # Set context for this request
        set_context(request_id=request_id, user_id=user_id)

        try:
            response = await call_next(request)

            # Calculate response time
            response_time = time.time() - start_time

            # Log response
            log_api_response(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=response.status_code,
                response_time=response_time,
                user_id=user_id,
                request_id=request_id,
            )

            return response

        except Exception as e:
            response_time = time.time() - start_time

            # Log error response
            log_api_response(
                method=request.method,
                endpoint=str(request.url.path),
                status_code=500,
                response_time=response_time,
                user_id=user_id,
                request_id=request_id,
                error=str(e),
            )

            error("Unhandled exception in API endpoint", exception=e)
            raise
        finally:
            # Clear context after request
            clear_context()


# Add logging middleware
app.add_middleware(LoggingMiddleware)

# Add session middleware (must be added after LoggingMiddleware to ensure proper order)
app.add_middleware(
    SessionMiddleware,
    secret_key=os.getenv("AUTH_CLIENT_SECRET"),
)


# Simple rate limiter
class RateLimiter:
    def __init__(self, requests_per_minute=60):
        self.requests_per_minute = requests_per_minute
        self.request_history = {}
        self.cleanup_interval = 60  # Cleanup old entries every minute
        self.last_cleanup = time.time()

    async def __call__(self, request: Request):
        # Clean up old entries
        current_time = time.time()
        if current_time - self.last_cleanup > self.cleanup_interval:
            await self._cleanup(current_time)
            self.last_cleanup = current_time

        # Get client IP
        client_ip = request.client.host

        # Check if client has exceeded rate limit
        if client_ip in self.request_history:
            requests = self.request_history[client_ip]
            # Remove requests older than 1 minute
            requests = [t for t in requests if current_time - t < 60]

            if len(requests) >= self.requests_per_minute:
                raise_http_exception(status_code=429, detail="Too many requests. Please try again later.")

            self.request_history[client_ip] = requests + [current_time]
        else:
            self.request_history[client_ip] = [current_time]

    async def _cleanup(self, current_time):
        """Remove entries older than 1 minute"""
        for ip in list(self.request_history.keys()):
            self.request_history[ip] = [t for t in self.request_history[ip] if current_time - t < 60]
            if not self.request_history[ip]:
                del self.request_history[ip]


# Initialize rate limiter
rate_limiter = RateLimiter()

# Load allowed users from the environment variable
ALLOWED_USERS = [user.strip() for user in os.getenv("USERS", "").split(",") if user.strip()]


# Define Pydantic models for request validation
class CreateQuizRequest(BaseModel):
    topic: str  # This is the user-defined description for the assessment
    quiz_name: str
    user_id: str
    skill_ids: List[int]  # Now properly used for many-to-many
    question_selection_mode: str = "dynamic"  # 'fixed' or 'dynamic'
    duration: int = 30  # Duration in minutes


class AnswerRequest(BaseModel):
    user_id: str
    question_id: str  # This is que_id from questions table
    answer: str
    session_code: str  # Changed from quiz_code
    time_taken: Optional[int] = None  # Time taken in seconds


class SessionCodeRequest(BaseModel):  # Renamed from QuizCodeRequest
    session_code: str


class StartSessionRequest(BaseModel):
    session_code: str


class SessionSubmissionRequest(BaseModel):
    session_code: str
    user_id: str


class UserCheckRequest(BaseModel):
    user_id: str


class FinalQuestionsRequest(BaseModel):
    question_ids: List[int]
    quiz_name: str
    assessment_id: Optional[int] = None  # For fixed assessments


class ReportRequest(BaseModel):
    report_type: str
    user_name: Optional[str] = None
    # This refers to assessment name or part of it
    report_topic: Optional[str] = None
    # For assessment-wise reports
    assessment_base_name: Optional[str] = None
    quiz_type: Optional[str] = None


class TaskResponse(BaseModel):
    """Response model for task submission"""

    task_id: str
    status: str
    message: str


def get_questions_by_difficulty(all_questions, difficulty):
    """Filter questions based on the specified difficulty level."""
    return [q for q in all_questions if q["level"] == difficulty]


def _validate_session_and_user(session_code: str, user_id: str) -> tuple[str, dict, str]:
    """Validate session and user, return normalized session code, session details, and validated user_id."""
    normalized_session_code = validate_session_code_format(session_code)

    session_details = get_session_details(normalized_session_code)
    if not session_details:
        raise_http_exception(status_code=404, detail="Invalid session code")

    session_user_id = get_session_user_id(normalized_session_code)
    if not session_user_id:
        raise_http_exception(status_code=404, detail="User not found for this session")

    if session_user_id != user_id:
        warning(f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}")

    return normalized_session_code, session_details, session_user_id


def _find_question(question_topic_identifier: str, question_id: str) -> dict:
    """Find question by topic and ID, with fallback to ID-only lookup."""
    # First try the original method (for backward compatibility)
    question = get_questions_for_check(question_topic_identifier, question_id)

    # If not found and this is a skill-based assessment, try looking up by question_id only
    if not question:
        question = get_single_row(
            """
            SELECT que_id, question, options, answer, level, topic
            FROM questions
            WHERE que_id = %s
            """,
            (int(question_id),),
            as_dict=True,
        )

    if not question:
        raise_http_exception(status_code=404, detail="Question not found")

    return question


def _check_answer_correctness(answer: str, question: dict) -> tuple[bool, str, str]:
    """Check if answer is correct and return correctness, answer key, and answer value."""
    correct_answer_key = question["answer"]
    correct_answer_value = safe_json_loads(question["options"], {}).get(correct_answer_key, "")
    is_correct = answer.lower() == correct_answer_key.lower()

    return is_correct, correct_answer_key, correct_answer_value


def _get_session_response_data(
    session_details: dict,
    is_correct: bool,
    correct_answer_key: str,
    correct_answer_value: str,
    normalized_session_code: str,
) -> dict:
    """Get response data for the session."""
    current_score = get_session_score(normalized_session_code)
    remaining_time_seconds = session_details.get("remaining_time_seconds", 0)

    attempted_questions_count = get_single_value(
        """SELECT COUNT(*) FROM user_answers WHERE session_id = %s""", (session_details["session_id"],), default=0
    )

    question_selection_mode = get_single_value(
        "SELECT question_selection_mode FROM assessments WHERE id = %s",
        (session_details["assessment_id"],),
        default="dynamic",
    )

    return {
        "is_correct": is_correct,
        "correct_answer_key": correct_answer_key,
        "correct_answer_value": correct_answer_value,
        "current_score": current_score,
        "remaining_time_seconds": remaining_time_seconds,
        "attempted_questions_count": attempted_questions_count,
        "question_selection_mode": question_selection_mode,
    }


def check_and_save_answer(
    user_id: str,
    question_id: str,
    answer: str,
    session_code: str,
    time_taken: Optional[int] = None,
):
    """
    Check the given answer and save the result to the database.
    This function is called internally by the endpoint.
    """
    try:
        # Validate session and user
        normalized_session_code, session_details, validated_user_id = _validate_session_and_user(session_code, user_id)

        # Extract topic identifier from assessment name
        assessment_name = session_details["assessment_name"]
        question_topic_identifier = assessment_name.replace(" Assessment", "")

        # Find the question
        question = _find_question(question_topic_identifier, question_id)

        # Check answer correctness
        is_correct, correct_answer_key, correct_answer_value = _check_answer_correctness(answer, question)

        # Save result to database
        quiz_type = "assessment"  # Always use 'assessment' instead of 'mock' or 'final'
        save_result_to_db(
            user_id=validated_user_id,
            question_id_int=question["que_id"],
            answer=answer,
            is_correct=is_correct,
            session_code=normalized_session_code,
            time_taken=time_taken,
            legacy_topic=question_topic_identifier,
            legacy_quiz_type=quiz_type,
            question_level=question["level"],
            question_text=str(question["question"]),
            question_options_json=safe_json_dumps(question["options"]),
            question_correct_answer_key=correct_answer_key,
        )

        # Get response data
        response_data = _get_session_response_data(
            session_details, is_correct, correct_answer_key, correct_answer_value, normalized_session_code
        )

        return success_response(data=response_data, message="Answer checked and saved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error("Error in check_and_save_answer_internal", exception=e)
        raise HTTPException(status_code=500, detail={"error": str(e)})


def _determine_result_status(answer: str, is_correct: bool) -> str:
    """Determine result status based on answer and correctness."""
    lowercased_answer = answer.lower()
    if lowercased_answer == "timeout":
        return "Timeout"
    elif is_correct:
        return "Correct"
    else:
        return "Incorrect"


def _save_to_legacy_table(
    user_id: str,
    legacy_topic: str,
    question_level: str,
    legacy_quiz_type: str,
    question_id_int: int,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
    answer: str,
    result_status: str,
    score: float,
):
    """Save result to legacy user_assessment table for backward compatibility."""
    insert_user_data(
        {
            "user_id": user_id,
            "topic": legacy_topic,
            "level": question_level,
            "quiz_type": legacy_quiz_type,
            "que_id": question_id_int,
            "question": question_text,
            "options": safe_json_loads(question_options_json, {}),
            "correct_answer": question_correct_answer_key,
            "user_answer": "None" if answer.lower() == "timeout" else answer,
            "result": result_status,
            "score": score,
        }
    )


def _save_to_user_answers_table(
    session_code: str, question_id_int: int, answer: str, is_correct: bool, score: float, time_taken: Optional[int]
):
    """Save result to user_answers table."""
    session_details = get_session_details(session_code)
    if not session_details:
        raise ValueError(f"Session code {session_code} not found during save_result_to_db")

    internal_session_id = session_details["session_id"]

    execute_query(
        """INSERT INTO user_answers
           (session_id, question_id, user_answer, is_correct, score, time_taken, created_at)
           VALUES (%s, %s, %s, %s, %s, %s, CURRENT_TIMESTAMP)
           ON CONFLICT (session_id, question_id)
           DO UPDATE SET user_answer = EXCLUDED.user_answer,
                         is_correct = EXCLUDED.is_correct,
                         score = EXCLUDED.score,
                         time_taken = EXCLUDED.time_taken""",
        (
            internal_session_id,
            question_id_int,
            answer,
            is_correct,
            score,
            time_taken,
        ),
        commit=True,
    )


def save_result_to_db(
    user_id: str,
    question_id_int: int,
    answer: str,
    is_correct: bool,
    session_code: str,
    time_taken: Optional[int],
    legacy_topic: str,
    legacy_quiz_type: str,
    question_level: str,
    question_text: str,
    question_options_json: str,
    question_correct_answer_key: str,
):
    """Save quiz answer results to the database using the new schema."""
    try:
        # Determine result status
        result_status = _determine_result_status(answer, is_correct)

        # Calculate score
        score = calculate_question_score(question_level, result_status.lower())

        # Save to legacy table for backward compatibility
        _save_to_legacy_table(
            user_id,
            legacy_topic,
            question_level,
            legacy_quiz_type,
            question_id_int,
            question_text,
            question_options_json,
            question_correct_answer_key,
            answer,
            result_status,
            score,
        )

        # Save to user_answers table
        _save_to_user_answers_table(session_code, question_id_int, answer, is_correct, score, time_taken)

        debug("Answer saved successfully to database")

    except Exception as e:
        error("Error saving result", exception=e)
        raise


async def apply_migrations():
    """Apply database migrations from the migrations directory"""
    info("Checking for database migrations to apply...")

    try:
        # Connect to the database
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Create migrations table if it doesn't exist
                cur.execute(
                    """
                    CREATE TABLE IF NOT EXISTS migrations (
                        id SERIAL PRIMARY KEY,
                        name TEXT UNIQUE NOT NULL,
                        batch INTEGER NOT NULL,
                        applied_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                    )
                """
                )

                # Get list of applied migrations
                cur.execute("SELECT name FROM migrations")
                applied_migrations = {row[0] for row in cur.fetchall()}

                # Get list of migration files

                current_dir = os.path.dirname(os.path.abspath(__file__))
                # Get the absolute path of the project root (/app)
                project_root = os.path.dirname(current_dir)
                # Build the correct, absolute path to the migrations directory
                migrations_dir = os.path.join(project_root, "database", "migrations")

                if not os.path.exists(migrations_dir):
                    warning(f"Migrations directory '{migrations_dir}' not found")
                    return

                migration_files = sorted([f for f in os.listdir(migrations_dir) if f.endswith(".sql")])

                # Apply new migrations
                applied_count = 0
                for migration_file in migration_files:
                    if migration_file not in applied_migrations:
                        info(f"Applying migration: {migration_file}")

                        # Read migration file
                        with open(os.path.join(migrations_dir, migration_file), "r") as f:
                            sql_content = f.read()

                        # Apply migration
                        cur.execute(sql_content)

                        # Get the current highest batch number
                        cur.execute("SELECT COALESCE(MAX(batch), 0) FROM migrations")
                        current_batch = cur.fetchone()[0]
                        next_batch = current_batch + 1

                        # Record migration as applied
                        cur.execute(
                            "INSERT INTO migrations (name, batch) VALUES (%s, %s)",
                            (migration_file, next_batch),
                        )

                        applied_count += 1

                conn.commit()

                if applied_count > 0:
                    info(f"Applied {applied_count} new migrations")
                else:
                    info("No new migrations to apply")

    except Exception as e:
        error("Failed to apply migrations", exception=e)
        raise


def _parse_user_identifier(user_id: str) -> tuple[str, str, str]:
    """Parse user identifier and return email, username, and external_id."""
    if "@" in user_id:
        email = user_id
        username = user_id.split("@")[0]
        external_id = user_id  # Use full email as external_id to avoid conflicts
    else:
        email = None
        username = user_id
        external_id = username

    return email, username, external_id


def _find_user_by_email(cur, email: str) -> int:
    """Find user by email and return user ID if found."""
    if not email:
        return None

    cur.execute("SELECT id, external_id FROM users WHERE email = %s", (email,))
    result = cur.fetchone()
    return result[0] if result else None


def _find_user_by_external_id(cur, username: str) -> int:
    """Find user by external_id and return user ID if found."""
    cur.execute("SELECT id FROM users WHERE external_id = %s", (username,))
    result = cur.fetchone()
    return result[0] if result else None


def _update_user_email_if_needed(cur, conn, user_id: int, email: str):
    """Update user email if user exists but doesn't have email set."""
    if email:
        cur.execute(
            "UPDATE users SET email = %s WHERE id = %s AND (email IS NULL OR email = '')",
            (email, user_id),
        )
        conn.commit()


def _create_new_user(cur, conn, external_id: str, email: str, username: str) -> int:
    """Create a new user and return the internal ID."""
    cur.execute(
        "INSERT INTO users (external_id, email, display_name) VALUES (%s, %s, %s) RETURNING id",
        (external_id, email, username),
    )
    user_internal_id = cur.fetchone()[0]
    conn.commit()
    return user_internal_id


def get_or_create_user(user_id):
    """
    Get or create a user record and return the internal ID.
    If user_id is an email, extract the username part (before @) for external_id and display_name.
    First checks if a user with the same email exists, then checks by external_id.
    """
    try:
        # Parse user identifier
        email, username, external_id = _parse_user_identifier(user_id)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # First check if a user with this email exists
                user_id_by_email = _find_user_by_email(cur, email)
                if user_id_by_email:
                    return user_id_by_email

                # Then check if user exists by external_id
                user_id_by_external = _find_user_by_external_id(cur, username)
                if user_id_by_external:
                    # Update email if needed
                    _update_user_email_if_needed(cur, conn, user_id_by_external, email)
                    return user_id_by_external

                # Create new user
                return _create_new_user(cur, conn, external_id, email, username)

    except Exception as e:
        error(f"Error in get_or_create_user: {e}")
        raise


def _validate_assessment_params(question_selection_mode: str):
    """Validate assessment parameters."""
    if question_selection_mode not in ["fixed", "dynamic"]:
        raise ValueError("question_selection_mode must be either 'fixed' or 'dynamic'")


def _generate_assessment_names_and_description(
    assessment_topic_name: str, is_final: bool, description: Optional[str]
) -> tuple[str, str]:
    """Generate full assessment name and description."""
    full_assessment_name = f"{assessment_topic_name} {'Final' if is_final else 'Mock'} Assessment"
    assessment_description = description or f"{'Final' if is_final else 'Mock'} assessment for {assessment_topic_name}"
    return full_assessment_name, assessment_description


def _create_skill_associations(cur, assessment_id: int, skill_ids: List[int]):
    """Create skill associations for an assessment."""
    for skill_id in skill_ids:
        cur.execute(
            """INSERT INTO assessment_skills
            (assessment_id, skill_id)
            VALUES (%s, %s) ON CONFLICT DO NOTHING""",
            (assessment_id, skill_id),
        )


def _create_new_assessment(
    cur,
    full_assessment_name: str,
    assessment_description: str,
    is_final: bool,
    total_questions: int,
    question_selection_mode: str,
) -> int:
    """Create a new assessment in the database."""
    composition = {"basic": 6, "intermediate": 6, "advanced": 8}

    cur.execute(
        """INSERT INTO assessments
           (name, description, is_final, total_questions, question_selection_mode, composition)
           VALUES (%s, %s, %s, %s, %s, %s) RETURNING id""",
        (
            full_assessment_name,
            assessment_description,
            is_final,
            total_questions,
            question_selection_mode,
            json.dumps(composition),
        ),
    )
    return cur.fetchone()[0]


def _log_assessment_creation(
    full_assessment_name: str, assessment_description: str, total_questions: int, assessment_id: int
):
    """Log assessment creation with error handling."""
    try:
        insert_quiz_creation_logs(
            [
                {
                    "user_id": "system",  # Default user for system-created assessments
                    "assessment_name": full_assessment_name,
                    "assessment_description": assessment_description,
                    "total_questions": total_questions,
                    "assessment_id": assessment_id,
                }
            ]
        )
    except Exception as log_error:
        # Don't fail the assessment creation if logging fails
        warning(f"Failed to log assessment creation: {log_error}")


def get_or_create_assessment(
    assessment_topic_name: str,
    is_final=False,
    description: Optional[str] = None,
    skill_ids: List[int] = [],
    question_selection_mode: str = "dynamic",
):
    """
    Create assessment with multiple skills.

    Args:
        assessment_topic_name: Base name for the assessment
        is_final: Whether this is a final assessment
        description: Optional description for the assessment
        skill_ids: List of skill IDs to associate with this assessment
        question_selection_mode: How questions are selected - 'fixed' or 'dynamic'

    Returns:
        The ID of the created or existing assessment
    """
    try:
        # Validate parameters
        _validate_assessment_params(question_selection_mode)

        # Get configuration
        total_questions = int(os.getenv("TOTAL_QUESTIONS_COUNT", "30"))

        # Generate names and description
        full_assessment_name, assessment_description = _generate_assessment_names_and_description(
            assessment_topic_name, is_final, description
        )

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if assessment exists
                cur.execute("SELECT id FROM assessments WHERE name = %s", (full_assessment_name,))
                result = cur.fetchone()

                if result:
                    assessment_id = result[0]
                    # Ensure skill associations exist
                    _create_skill_associations(cur, assessment_id, skill_ids)
                    return assessment_id

                # Create new assessment
                assessment_id = _create_new_assessment(
                    cur,
                    full_assessment_name,
                    assessment_description,
                    is_final,
                    total_questions,
                    question_selection_mode,
                )

                # Create skill associations
                _create_skill_associations(cur, assessment_id, skill_ids)

                conn.commit()

                # Log the assessment creation
                _log_assessment_creation(full_assessment_name, assessment_description, total_questions, assessment_id)

                return assessment_id

    except Exception as e:
        error(f"Error in get_or_create_assessment for '{assessment_topic_name}': {e}", exc_info=True)
        raise


def _find_session_by_code(cur, session_code: str) -> tuple:
    """Find session by code and return session details."""
    cur.execute(
        "SELECT id, user_id, assessment_id FROM sessions WHERE code = %s",
        (session_code,),
    )
    return cur.fetchone()


def _validate_session_ownership(
    session_code: str, session_user_id: int, session_assessment_id: int, internal_user_id: int, assessment_id: int
):
    """Validate that session belongs to the correct user and assessment."""
    if session_user_id != internal_user_id or session_assessment_id != assessment_id:
        error(
            f"Session code {session_code} mismatch: user ({session_user_id} vs {internal_user_id}) "
            f"or assessment ({session_assessment_id} vs {assessment_id})"
        )
        raise HTTPException(
            status_code=403,
            detail="Session code mismatch or unauthorized.",
        )


def _update_session_to_in_progress(cur, conn, session_db_id: int) -> int:
    """Update session status to in_progress and return session ID."""
    cur.execute(
        """UPDATE sessions SET status = 'in_progress',
           started_at = COALESCE(started_at, CURRENT_TIMESTAMP AT TIME ZONE 'UTC')
           WHERE id = %s AND (status = 'pending' OR status = 'in_progress') RETURNING id""",
        (session_db_id,),
    )
    updated_session = cur.fetchone()
    conn.commit()

    if updated_session:
        return updated_session[0]
    else:
        # Session might be completed or expired, return existing id anyway
        cur.execute("SELECT id FROM sessions WHERE id = %s", (session_db_id,))
        return cur.fetchone()[0]


def get_or_create_session(user_id_external: str, assessment_id: int, session_code: str):
    """
    Get or create a session for the user and assessment.
    If session exists by code, it updates its status to 'in_progress' if 'pending'.
    """
    conn = None
    try:
        internal_user_id = get_or_create_user(user_id_external)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                # Check if session exists for this code
                session_result = _find_session_by_code(cur, session_code)

                if session_result:
                    session_db_id, session_user_id, session_assessment_id = session_result

                    # Validate session ownership
                    _validate_session_ownership(
                        session_code, session_user_id, session_assessment_id, internal_user_id, assessment_id
                    )

                    # Update session to in_progress
                    return _update_session_to_in_progress(cur, conn, session_db_id)

                # Session code does not exist
                error(f"Session code {session_code} not found. Cannot create on-the-fly in get_or_create_session.")
                return error_response(
                    message="Invalid session code.",
                    code=status.HTTP_404_NOT_FOUND,
                    error_type="NotFound",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error in get_or_create_session: {e}", exc_info=True)
        if conn:
            conn.rollback()
        return error_response(
            message="Error processing session.",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


def expire_sessions():
    """
    Background task to mark sessions as expired if past completed_at and not completed.
    This function runs periodically to check for expired sessions.
    """
    try:
        debug("Running session expiry check...")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Find sessions that are past their completed_at time and still in progress
                cur.execute(
                    """UPDATE sessions
                       SET status = 'expired'
                       WHERE status = 'in_progress'
                       AND completed_at IS NOT NULL
                       AND completed_at < CURRENT_TIMESTAMP
                       RETURNING id, code""",
                )
                expired_sessions = cur.fetchall()

                if expired_sessions:
                    info(f"Expired {len(expired_sessions)} sessions: {[s['code'] for s in expired_sessions]}")
                else:
                    debug("No sessions to expire")

                conn.commit()

    except Exception as e:
        error(f"Error during session expiry check: {str(e)}", exc_info=True)


async def periodic_session_expiry():
    """
    Periodic task to run session expiry checks every 5 minutes.
    """
    while True:
        try:
            expire_sessions()
            # Wait 5 minutes before next check
            await asyncio.sleep(300)
        except Exception as e:
            error(f"Error in periodic session expiry: {str(e)}")
            # Wait 1 minute before retrying on error
            await asyncio.sleep(60)


# Set up authentication on API router
setup_auth(api_router)
info("Authentication setup complete")


@app.on_event("startup")
async def startup_event():
    info("Starting server...")

    # Give Dapr sidecar a moment to start (configurable delay)
    startup_delay = float(os.getenv("DAPR_STARTUP_DELAY", "0"))
    if startup_delay > 0:
        await asyncio.sleep(startup_delay)

    # Validate environment variables
    if not check_environment():
        error("Environment validation failed. Server may not function correctly.")
        raise RuntimeError("Environment validation failed.")

    # Start the periodic session expiry task
    try:
        asyncio.create_task(periodic_session_expiry())
        info("Started periodic session expiry task")
    except Exception as e:
        error(f"Failed to start periodic session expiry task: {str(e)}")

    # Initialize database connection pool
    try:
        await get_connection_pool()
        info("Database connection pool initialized successfully.")
    except Exception as e:
        error(f"Failed to initialize database connection pool: {str(e)}")

    # Start Dapr connectivity check in background (non-blocking)
    info("Starting Dapr connectivity check in background...")
    asyncio.create_task(initialize_dapr_connectivity())

    info("Application startup completed - service is ready to serve requests")


# =============================================================================
# DAPR PUBSUB FUNCTIONS
# =============================================================================


async def check_dapr_health() -> bool:
    """
    Check if Dapr sidecar is healthy and accessible.

    Returns:
        bool: True if Dapr is healthy, False otherwise
    """
    try:
        health_url = f"{DAPR_BASE_URL}/v1.0/healthz"
        timeout = httpx.Timeout(5.0, connect=2.0)

        async with httpx.AsyncClient(timeout=timeout) as client:
            response = await client.get(health_url)
            return response.status_code == 204

    except Exception:
        return False


async def initialize_dapr_connectivity():
    """
    Initialize Dapr connectivity in the background without blocking application startup.
    """
    # Give Dapr sidecar a moment to start (configurable delay)
    startup_delay = float(os.getenv("DAPR_STARTUP_DELAY", "2"))
    if startup_delay > 0:
        await asyncio.sleep(startup_delay)

    # Wait for Dapr connectivity with exponential backoff
    await wait_for_dapr_connectivity(max_wait_time=90)


async def wait_for_dapr_connectivity(max_wait_time: int = 90) -> bool:
    """
    Wait for Dapr sidecar connectivity using exponential backoff.

    Args:
        max_wait_time: Maximum time to wait in seconds (default: 90 seconds)

    Returns:
        bool: True if Dapr is ready, False if timeout
    """
    # Exponential backoff: 1s, 2s, 4s, 8s, 15s, 15s, 15s...
    delays = [1, 2, 4, 8, 15]  # Then stick to 15s intervals
    start_time = time.time()
    attempt = 0

    info("Waiting for Dapr sidecar...")

    while time.time() - start_time < max_wait_time:
        if await check_dapr_health():
            elapsed = int(time.time() - start_time)
            info(f"Dapr sidecar ready after {elapsed}s (attempt {attempt + 1})")
            return True

        # Calculate delay for this attempt
        delay = delays[min(attempt, len(delays) - 1)]

        # Log progress less frequently
        elapsed = int(time.time() - start_time)
        if attempt == 0 or elapsed % 30 == 0:  # Log every 30 seconds after first attempt
            remaining = max_wait_time - elapsed
            info(f"Still waiting for Dapr sidecar... ({elapsed}s elapsed, {remaining}s remaining)")

        await asyncio.sleep(delay)
        attempt += 1

    elapsed = int(time.time() - start_time)
    warning(f"Dapr sidecar not available after {elapsed}s - some features may not work")
    return False


async def publish_question_generation_task(task_data: dict, max_retries: int = 3) -> bool:
    """
    Publish a question generation task to Dapr pubsub with retry logic.

    Args:
        task_data: Dictionary containing task information
        max_retries: Maximum number of retry attempts

    Returns:
        bool: True if published successfully, False otherwise
    """
    dapr_url = f"{DAPR_BASE_URL}/v1.0/publish/{PUBSUB_NAME}/{QUESTION_TASKS_TOPIC}"

    for attempt in range(max_retries + 1):
        try:
            # Use shorter timeouts with retries
            timeout = httpx.Timeout(10.0, connect=5.0)

            async with httpx.AsyncClient(timeout=timeout) as client:
                response = await client.post(
                    dapr_url,
                    json=task_data,
                    headers={"Content-Type": "application/json"},
                )

                if response.status_code == 204:  # Dapr returns 204 for successful publish
                    task_id = task_data.get("task_id")
                    if attempt > 0:
                        info(f"Successfully published task {task_id} to Dapr pubsub (attempt {attempt + 1})")
                    else:
                        info(f"Successfully published task {task_id} to Dapr pubsub")
                    return True
                else:
                    error(f"Failed to publish task to Dapr: {response.status_code} - {response.text}")
                    if attempt == max_retries:
                        return False

        except httpx.ConnectError as e:
            if attempt == max_retries:
                error(f"Connection error when publishing to Dapr: {e}")
                return False

        except httpx.TimeoutException as e:
            if attempt == max_retries:
                error(f"Timeout error when publishing to Dapr: {e}")
                return False

        except httpx.RequestError as e:
            if attempt == max_retries:
                error(f"Network error when publishing to Dapr: {e}")
                return False

        except Exception as e:
            if attempt == max_retries:
                error(f"Unexpected error publishing to Dapr: {e}")
                return False

        # Wait before retrying (exponential backoff)
        if attempt < max_retries:
            wait_time = 2**attempt  # 1s, 2s, 4s
            await asyncio.sleep(wait_time)

    return False


# =============================================================================
# QUESTION GENERATION API ENDPOINTS
# =============================================================================


@api_router.post("/generate-questions", response_model=TaskResponse)
async def generate_questions_async(request: QuestionGenerationTaskRequest, _: None = Depends(rate_limiter)):
    """
    Publish a question generation task to Dapr pubsub for async processing.
    This follows the new architecture where the API publishes tasks and the worker processes them.
    """
    try:
        # Convert skill_id to numeric format if it's a hash ID
        skill_id = request.skill_id
        if isinstance(skill_id, str):
            try:
                decoded_id = decode_skill_id(skill_id)
                if decoded_id is not None:
                    skill_id = decoded_id
                    info(f"Converted hash ID {request.skill_id} to numeric ID {decoded_id}")
                else:
                    # Try to convert to int directly
                    skill_id = int(skill_id)
            except ValueError:
                raise_http_exception(status_code=400, detail=f"Invalid skill_id format: {skill_id}")

        # Generate unique task ID
        task_id = str(uuid.uuid4())

        # Create task payload
        task_payload = {
            "task_id": task_id,
            "skill_id": skill_id,  # Use the converted numeric ID
            "skill_name": request.skill_name,
            "skill_description": request.skill_description,
            "created_at": datetime.utcnow().isoformat(),
        }

        info(f"Publishing question generation task {task_id} for skill: {request.skill_name}")

        # Check Dapr health before publishing
        if not await check_dapr_health():
            raise_http_exception(status_code=503, detail="Message broker service is unavailable")

        # Publish to Dapr pubsub with retry logic
        success = await publish_question_generation_task(task_payload)

        if success:
            return TaskResponse(
                task_id=task_id,
                status="published",
                message=f"Question generation task published successfully for skill: {request.skill_name}",
            )
        else:
            raise_http_exception(status_code=500, detail="Failed to publish question generation task")

    except Exception as e:
        error(f"Error in generate_questions_async: {e}")
        raise_http_exception(status_code=500, detail=f"Failed to publish question generation task: {str(e)}")


# =============================================================================
# ADMIN ENDPOINTS - Assessment Management
# =============================================================================


def _validate_quiz_request(request: CreateQuizRequest):
    """Validate the quiz creation request parameters."""
    assessment_description = request.topic
    user_defined_assessment_name = request.quiz_name

    if not assessment_description or len(assessment_description) < 20:
        raise_http_exception(status_code=400, detail="Valid assessment description (min 20 chars) is required")
    if not user_defined_assessment_name or len(user_defined_assessment_name) < 3:
        raise_http_exception(status_code=400, detail="Quiz name must be at least 3 characters")
    if not request.skill_ids:
        raise_http_exception(status_code=400, detail="At least one skill ID is required for quiz creation")
    if request.question_selection_mode not in ["fixed", "dynamic"]:
        raise HTTPException(
            status_code=400,
            detail="question_selection_mode must be either 'fixed' or 'dynamic'",
        )


def _validate_skills(skill_ids: list[int]) -> list:
    """Validate skill existence and description."""
    valid_skills = []
    for skill_id in skill_ids:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT id, description FROM skills WHERE id = %s", (skill_id,))
                skill = cur.fetchone()
                if not skill or not skill[1] or len(skill[1]) < 20:
                    raise HTTPException(
                        status_code=404,
                        detail=f"Skill {skill_id} not found or invalid description",
                    )
                valid_skills.append(skill)
    return valid_skills


def _check_question_availability(skill_ids: list[int], question_selection_mode: str) -> int:
    """Check if questions are available for the selected skills."""
    if question_selection_mode == "dynamic":
        question_count = count_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True)
    else:
        question_count = count_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=False)

    if question_count == 0:
        if question_selection_mode == "dynamic":
            raise HTTPException(
                status_code=400,
                detail="""No available questions found for the selected skills for dynamic assessment.
                All questions may be assigned to fixed assessments.
                Please select skills with available questions or create new questions.""",
            )
        else:
            raise HTTPException(
                status_code=400,
                detail="""No questions found for the selected skills.
                Please select skills with existing questions or create questions for these skills first.""",
            )

    return question_count


def _create_assessment_in_db(
    assessment_name: str, assessment_description: str, question_selection_mode: str, duration: int, skill_ids: list[int]
) -> int:
    """Create assessment in database and return assessment ID."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor() as cur:
            # Create the assessment
            cur.execute(
                """
                INSERT INTO assessments (
                    name, description, is_final, total_questions,
                    question_selection_mode, composition, duration_minutes
                ) VALUES (%s, %s, %s, %s, %s, %s, %s)
                RETURNING id
                """,
                (
                    assessment_name,
                    assessment_description,
                    False,  # is_final is no longer relevant but kept for DB compatibility
                    30,  # Default total_questions
                    question_selection_mode,
                    json.dumps({"basic": 10, "intermediate": 10, "advanced": 10}),
                    duration,
                ),
            )
            assessment_id = cur.fetchone()[0]

            # Create skill associations
            if skill_ids:
                # First, delete any existing skill associations for this assessment
                cur.execute(
                    """DELETE FROM assessment_skills WHERE assessment_id = %s""",
                    (assessment_id,),
                )
                # Insert each skill ID individually
                for skill_id in skill_ids:
                    cur.execute(
                        """INSERT INTO assessment_skills (assessment_id, skill_id)
                           VALUES (%s, %s) ON CONFLICT DO NOTHING""",
                        (assessment_id, skill_id),
                    )

            conn.commit()

    return assessment_id


@api_router.post("/admin/quiz")
async def create_quiz(request: CreateQuizRequest, _: None = Depends(rate_limiter)):
    """
    Creates a new assessment based on the provided description and quiz name.

    Args:
        request (CreateQuizRequest): The request body containing:
            topic (str): The user-defined description for the assessment.
            quiz_name (str): The user-defined name for this assessment.
            user_id (str): The ID of the admin creating the quiz.
            skill_ids (list[int]): List of skill IDs to associate with this assessment.

    Returns:
        JSONResponse: A JSON response containing the ID of the created assessment,
        and a success message.
        Admins should then use the /generate_sessions endpoint to create usable session codes.

    Raises:
        HTTPException:
            - 400: If the description is missing or no questions are found.
            - 500: If an error occurs during quiz creation.
    """
    try:
        # Validate request parameters
        _validate_quiz_request(request)

        # Extract request data
        assessment_description = request.topic
        user_defined_assessment_name = request.quiz_name
        user_id = request.user_id
        skill_ids = request.skill_ids
        question_selection_mode = request.question_selection_mode
        duration = request.duration

        # Validate skills
        _validate_skills(skill_ids)

        # Check question availability
        question_count = _check_question_availability(skill_ids, question_selection_mode)
        info(f"Found {question_count} existing questions for the selected skills")

        # Create assessment name with timestamp
        timestamp = datetime.now().strftime("%d_%m_%Y")
        assessment_base_name = f"{user_defined_assessment_name}_{timestamp}"
        assessment_name = f"{assessment_base_name} Assessment"

        # Create assessment in database
        assessment_id = _create_assessment_in_db(
            assessment_name, assessment_description, question_selection_mode, duration, skill_ids
        )

        # Log the assessment creation
        insert_quiz_creation_logs(
            [
                {
                    "user_id": user_id,
                    "assessment_name": assessment_name,
                    "assessment_description": assessment_description,
                    "total_questions": question_count,
                    "assessment_id": assessment_id,
                }
            ]
        )

        return success_response(
            data={
                "assessment_id": assessment_id,
                "assessment_base_name": assessment_base_name,
                "question_selection_mode": question_selection_mode,
                "skill_ids": skill_ids,
                "total_questions_available": question_count,
                "duration": duration,
            },
            message="Assessment created successfully. Please generate sessions to get usable codes.",
        )

    except Exception as e:
        error(f"Error creating quiz: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Error creating assessment: {str(e)}")


@api_router.get("/get_value")
def get_value():
    """
    Retrieve assessment question count and time from environment variables.

    Returns:
        dict: A standardized response containing the total questions count and question time.
    """
    total_count = os.getenv("TOTAL_QUESTIONS_COUNT", "30")
    question_time = os.getenv("QUESTION_TIME", "0")

    return success_response(
        data={
            "total_questions_count": total_count,
            "question_time": question_time,
        },
        message="Configuration retrieved successfully",
    )


@api_router.post("/validate_session_code")
def validate_session_code(request: SessionCodeRequest):
    """
    Validates if a session code exists and returns basic session/assessment details.
    Does NOT update the session status.

    Args:
        request (SessionCodeRequest): The request body containing the session_code.

    Returns:
        dict: A dictionary containing basic session and assessment details if the code is valid.

    Raises:
        HTTPException:
            - 400: If the session code is missing or invalid format.
            - 404: If the session code doesn't exist.
            - 500: If there's a database error.
    """
    session_code_input = request.session_code

    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        # Decode session code (handles both raw 6-digit codes and hashes)
        session_code = validate_session_code_format(session_code_input)
        session_details = get_session_and_assessment_details_by_code(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        # Get started_at and completed_at for the response
        started_at = None
        completed_at = None

        if "started_at" in session_details and session_details["started_at"]:
            started_at = session_details["started_at"].isoformat()

        if "completed_at" in session_details and session_details["completed_at"]:
            completed_at = session_details["completed_at"].isoformat()

        # Get assessment details including question_selection_mode
        question_selection_mode = "dynamic"  # Default to dynamic
        try:
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        "SELECT question_selection_mode FROM assessments WHERE id = %s",
                        (session_details["assessment_id"],),
                    )
                    assessment_details = cur.fetchone()
                    if assessment_details and assessment_details["question_selection_mode"]:
                        question_selection_mode = assessment_details["question_selection_mode"]
        except Exception as e:
            error(f"Error fetching assessment details in validate_session_code: {e}")
            # Continue with default value if there's an error

        response_data = {
            "session_id": session_details["session_id"],
            # Add the original session code for users to see
            "session_code": request.session_code,
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],
            # Remove sensitive user data - only include username for display
            "username": session_details.get("username", ""),
            "session_status": session_details["session_status"],
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "attempted_questions_count": len(session_details.get("attempted_questions", [])),
            "started_at": started_at,
            "completed_at": completed_at,
            "question_selection_mode": question_selection_mode,
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session code validated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error validating session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


@api_router.post("/start_session")
def start_session(request: StartSessionRequest):
    """
    Starts a session by updating its status to 'in_progress' and setting timestamps.

    Args:
        request (StartSessionRequest): The request body containing the session_code.

    Returns:
        dict: A dictionary containing updated session details.

    Raises:
        HTTPException:
            - 400: If the session code is missing or invalid format.
            - 404: If the session code doesn't exist.
            - 409: If the session is already in progress or completed.
            - 500: If there's a database error.
    """
    session_code_input = request.session_code

    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")

    try:
        # Decode session code (handles both raw 6-digit codes and hashes)
        session_code = validate_session_code_format(session_code_input)
        session_details = get_session_and_assessment_details_by_code(session_code)

        if not session_details:
            raise_http_exception(status_code=404, detail="Invalid or expired session code.")

        # Check if session can be started
        if session_details["session_status"] != "pending":
            raise_http_exception(
                status_code=409,
                detail=f"Session cannot be started. Current status: {session_details['session_status']}",
            )

        # Update session status to 'in_progress'
        try:
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    # Get assessment duration for reference (not used in this context)
                    cur.execute(
                        """SELECT duration_minutes FROM assessments
                           WHERE id = %s""",
                        (session_details["assessment_id"],),
                    )

                    # Only set started_at when starting a new session
                    # Don't set completed_at as we calculate remaining time based on started_at
                    cur.execute(
                        """UPDATE sessions
                           SET status = 'in_progress',
                               started_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                           WHERE id = %s AND status = 'pending'""",
                        (session_details["session_id"],),
                    )
                    conn.commit()

                    # Refresh session details to get updated timestamps and status
                    session_details = get_session_and_assessment_details_by_code(session_code)

                    info(
                        f"Started session {session_code} with {session_details.get('remaining_time_seconds', 0)} "
                        f"seconds remaining"
                    )
        except Exception as e:
            error(f"Error updating session status for code {session_code}: {e}")
            raise_http_exception(status_code=500, detail=f"Failed to start session: {str(e)}")

        # Get started_at and completed_at for the response
        started_at = None
        completed_at = None

        if "started_at" in session_details and session_details["started_at"]:
            started_at = session_details["started_at"].isoformat()

        if "completed_at" in session_details and session_details["completed_at"]:
            completed_at = session_details["completed_at"].isoformat()

        response_data = {
            "session_id": session_details["session_id"],
            "session_code": session_code,  # Add the original session code for users to see
            "assessment_id": session_details["assessment_id"],
            "assessment_name": session_details["assessment_name"],
            "is_final": session_details["is_final"],
            # Remove sensitive user data - only include username for display
            "username": session_details.get("username", ""),
            "session_status": session_details["session_status"],
            "remaining_time_seconds": session_details.get("remaining_time_seconds", 0),
            "attempted_questions_count": len(session_details.get("attempted_questions", [])),
            "started_at": started_at,
            "completed_at": completed_at,
        }

        info(f"Session {session_code} has {response_data['remaining_time_seconds']} seconds remaining")
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Session started successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error starting session {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Server error: {str(e)}")


def _validate_session_for_question(session_code: str) -> tuple[str, dict]:
    """Validate session and return decoded session code and session details."""
    decoded_session_code = validate_session_code_format(session_code)
    session_details = get_session_and_assessment_details_by_code(decoded_session_code)

    if not session_details:
        return None, error_response(
            message="Invalid or expired session code.",
            code=status.HTTP_404_NOT_FOUND,
            error_type="NotFound",
        )

    return decoded_session_code, session_details


def _get_session_user_id(session_details: dict) -> str:
    """Get the external user ID for the session."""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT u.external_id FROM users u WHERE u.id = %s",
                    (session_details["user_id"],),
                )
                user_result = cur.fetchone()
                if user_result:
                    return user_result["external_id"]
                else:
                    raise_http_exception(status_code=404, detail="User not found for this session")
    except Exception as e:
        error(f"Error fetching user for session: {e}")
        raise_http_exception(status_code=500, detail=f"Error fetching user for session: {str(e)}")


def _get_assessment_details(assessment_id: int) -> dict:
    """Get assessment details including question selection mode."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                (assessment_id,),
            )
            return cur.fetchone()


def _get_questions_for_assessment(
    assessment_id: int, is_fixed_assessment: bool, question_topic_identifier: str, is_final: bool
) -> list:
    """Get all available questions for the assessment."""
    if is_fixed_assessment:
        return fetch_questions_for_fixed_assessment(assessment_id)
    else:
        # For dynamic assessments, get questions based on skills
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                    (assessment_id,),
                )
                results = cur.fetchall()
                skill_ids = [row[0] for row in results] if results else []

        if skill_ids:
            return fetch_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True)
        else:
            if is_final:
                return fetch_final_questions(question_topic_identifier)
            else:
                return fetch_dynamic_questions_excluding_fixed(question_topic_identifier)


def _filter_questions(all_questions: list, attempted_questions: list, difficulty: str, retake: bool) -> list:
    """Filter questions based on attempts, difficulty, and retake settings."""
    filtered_questions = []
    for question in all_questions:
        # Skip attempted questions unless retake is allowed
        if question["que_id"] in attempted_questions and not retake:
            continue

        # Filter by difficulty if specified
        if difficulty != "all" and question["level"] != difficulty:
            continue

        filtered_questions.append(question)

    return filtered_questions


def _get_total_questions_count(assessment_id: int, is_fixed_assessment: bool) -> int:
    """Get total questions count for the assessment."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor() as cur:
            if is_fixed_assessment:
                cur.execute(
                    "SELECT COUNT(*) FROM assessment_questions WHERE assessment_id = %s",
                    (assessment_id,),
                )
                return cur.fetchone()[0]
            else:
                cur.execute(
                    "SELECT total_questions FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                result = cur.fetchone()
                return result[0] if result and result[0] else None


def _calculate_current_score(session_id: int) -> float:
    """Calculate current score from user answers."""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute(
                    "SELECT SUM(score) as total_score FROM user_answers WHERE session_id = %s",
                    (session_id,),
                )
                score_result = cur.fetchone()
                return float(score_result[0]) if score_result and score_result[0] else 0
    except Exception as e:
        error(f"Error calculating current score in get_next_question: {e}")
        return 0


def _format_question_response(
    next_question: dict,
    session_details: dict,
    assessment_details: dict,
    attempted_questions: list,
    total_questions: int,
    current_score: float,
) -> dict:
    """Format the response data for the next question."""
    # Filter out sensitive fields from question data
    filtered_question = {
        "que_id": next_question["que_id"],
        "topic": next_question["topic"],
        "level": next_question["level"],
        "question": next_question["question"],
        "options": next_question["options"],
    }

    # Get question selection mode
    question_selection_mode = "dynamic"
    if assessment_details and "question_selection_mode" in assessment_details:
        question_selection_mode = assessment_details["question_selection_mode"]

    return {
        "quiz_type": "final" if session_details["is_final"] else "mock",
        "quiz_name": session_details["assessment_name"],
        "topic": session_details["assessment_name"].replace(" Mock Assessment", "").replace(" Final Assessment", ""),
        "question": filtered_question,
        "remaining_time_seconds": session_details.get("remaining_time_seconds", None),
        "attempted_questions_count": len(attempted_questions),
        "total_questions": total_questions,
        "current_score": current_score,
        "question_selection_mode": question_selection_mode,
    }


# New endpoint for single question
@api_router.get("/get_next_question/{session_code}")
def get_next_question(
    session_code: str,
    user_id: str = Query(...),
    difficulty: str = Query("easy"),
    retake: bool = Query(False),
):
    """
    Fetches the next quiz question for the user session.

    Args:
        session_code (str): The session code or hash.
        user_id (str): The external user ID of the quiz taker.
        difficulty (str, optional): The difficulty level. Defaults to "easy".
        retake (bool, optional): Whether to allow retakes. Defaults to False.

    Returns:
        dict: A dictionary containing the next question and session info.

    Raises:
        HTTPException
    """
    try:
        # Validate session
        decoded_session_code, session_details = _validate_session_for_question(session_code)
        if not decoded_session_code:
            return session_details  # This is actually an error response

        # Validate user
        session_user_id = _get_session_user_id(session_details)
        if session_user_id and session_user_id != user_id:
            warning(f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}")
            user_id = session_user_id

        # Get assessment details
        assessment_id = session_details["assessment_id"]
        is_final = session_details["is_final"]
        question_topic_identifier = (
            session_details["assessment_name"].replace(" Mock Assessment", "").replace(" Final Assessment", "")
        )

        assessment_details = _get_assessment_details(assessment_id)
        is_fixed_assessment = assessment_details and assessment_details["question_selection_mode"] == "fixed"

        # Get attempted questions and all available questions
        attempted_questions = session_details.get("attempted_questions", [])
        all_questions_for_topic = _get_questions_for_assessment(
            assessment_id, is_fixed_assessment, question_topic_identifier, is_final
        )

        # Filter questions
        filtered_questions = _filter_questions(all_questions_for_topic, attempted_questions, difficulty, retake)

        # Check if questions are available
        if not filtered_questions:
            return error_response(
                message="No more questions available.",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Select next question randomly
        next_question = random.choice(filtered_questions)

        # Get additional data
        total_questions = _get_total_questions_count(assessment_id, is_fixed_assessment)
        current_score = _calculate_current_score(session_details["session_id"])

        # Format response
        response_data = _format_question_response(
            next_question, session_details, assessment_details, attempted_questions, total_questions, current_score
        )

        return success_response(data=response_data, message="Next question retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching next question for session code {session_code}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Failed to load next question: {str(e)}")


@api_router.get("/get_questions/{session_code}")  # Keep original endpoint for backward compatibility
def get_questions(
    session_code: str,  # Changed from quiz_code
    user_id: str = Query(...),  # External user_id of the quiz taker
    difficulty: str = Query("easy"),
    # Retake might be complex with new session model
    retake: bool = Query(False),
):
    """
    Fetches quiz questions based on the session code, user ID, difficulty.

    Args:
        session_code (str): The session code or hash.
        user_id (str): The external user ID of the quiz taker.
        difficulty (str, optional): The difficulty level. Defaults to "easy".
        retake (bool, optional): Whether to allow retakes. Defaults to False.
        (Note: retake logic might need review with session model)

    Returns:
        dict: A dictionary containing the quiz type, assessment name (as quiz_name),
              original topic (skill description), and a list of questions.

    Raises:
        HTTPException
    """

    try:
        # Decode session code (handles both raw 6-digit codes and hashes)
        decoded_session_code = validate_session_code_format(session_code)
        # Get session details including user_id
        session_details = get_session_and_assessment_details_by_code(decoded_session_code)
        if not session_details:
            return error_response(
                message="Invalid or expired session code.",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Always get the correct user_id from the session
        session_user_id = None
        try:
            # Get the external_id (username) for the user associated with this session
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute(
                        """
                        SELECT u.external_id
                        FROM users u
                        WHERE u.id = %s
                        """,
                        (session_details["user_id"],),
                    )
                    user_result = cur.fetchone()
                    if user_result:
                        session_user_id = user_result["external_id"]
                    else:
                        error(f"No user found for user_id: {session_details['user_id']}")
                        raise_http_exception(status_code=404, detail="User not found for this session")
        except Exception as e:
            error(f"Error fetching user for session: {e}")
            raise_http_exception(status_code=500, detail=f"Error fetching user for session: {str(e)}")

        # Always use the user_id from the session
        if session_user_id:
            if user_id and session_user_id != user_id:
                warning(f"User ID mismatch: Session belongs to {session_user_id} but request is from {user_id}")
            # Always use the session's user_id
            user_id = session_user_id

        # Auto-start session if it's in pending status
        # This ensures that dynamic assessments work properly with pause/resume functionality
        if session_details["session_status"] == "pending":
            info(f"Auto-starting pending session {decoded_session_code}")
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor() as cur:
                        # Update session status to 'in_progress' and set started_at
                        cur.execute(
                            """UPDATE sessions
                               SET status = 'in_progress',
                                   started_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                               WHERE code = %s AND status = 'pending'""",
                            (decoded_session_code,),
                        )
                        conn.commit()

                        if cur.rowcount > 0:
                            info(f"Successfully auto-started session {decoded_session_code}")
                            # Refresh session details to get updated status and timestamps
                            session_details = get_session_and_assessment_details_by_code(decoded_session_code)
                        else:
                            warning(
                                f"Failed to auto-start session {decoded_session_code} - session may have changed status"
                            )
            except Exception as e:
                error(f"Error auto-starting session {decoded_session_code}: {e}")
                # Continue with the original session details - don't fail the request

        # The 'topic' in questions table is assessment_base_name (e.g. UserAssessmentName_timestamp)
        # assessment_name in session_details is (e.g. UserAssessmentName_timestamp Mock Assessment)
        assessment_name_from_session = session_details["assessment_name"]
        question_topic_identifier = assessment_name_from_session.replace(" Mock Assessment", "").replace(
            " Final Assessment", ""
        )

        is_final = session_details["is_final"]
        quiz_type = "final" if is_final else "mock"  # For logic requiring 'mock' or 'final' string

        # Get assessment details including question_selection_mode
        assessment_id = session_details["assessment_id"]
        assessment_details = None
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment_details = cur.fetchone()

        # Check if this is a fixed assessment
        is_fixed_assessment = assessment_details and assessment_details["question_selection_mode"] == "fixed"

        # For fixed assessments, get the pre-selected questions from assessment_questions table
        if is_fixed_assessment:
            all_questions_for_topic = fetch_questions_for_fixed_assessment(assessment_id)
            info(f"Fetched {len(all_questions_for_topic)} fixed questions for assessment {assessment_id}")
            if len(all_questions_for_topic) > 0:
                info(f"Sample fixed question: {all_questions_for_topic[0].get('question', 'N/A')[:50]}...")
            else:
                warning(f"No fixed questions found for assessment {assessment_id}")
        else:
            # For dynamic assessments, get questions based on the skills associated with the assessment
            # First, get the skill IDs associated with this assessment
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        """SELECT skill_id FROM assessment_skills
                           WHERE assessment_id = %s""",
                        (assessment_id,),
                    )
                    results = cur.fetchall()
                    # Extract skill IDs from the results
                    skill_ids = []
                    if results:
                        for row in results:
                            skill_id = row[0]
                            skill_ids.append(skill_id)

            if skill_ids:
                # If we have skill IDs, fetch questions for these skills
                # For dynamic assessments, exclude questions that are assigned to fixed assessments

                all_questions_for_topic = fetch_questions_for_skills(skill_ids, exclude_fixed_assessment_questions=True)

                if len(all_questions_for_topic) > 0:
                    info(
                        f"Sample dynamic question for skills {skill_ids}: "
                        f"{all_questions_for_topic[0].get('question', 'N/A')[:50]}..."
                    )
                else:
                    warning(f"No dynamic questions found for skills {skill_ids}")
            else:
                # Fallback to the original logic if no skills are associated
                # For dynamic assessments, also exclude fixed assessment questions
                if is_final:
                    all_questions_for_topic = fetch_final_questions(question_topic_identifier)
                else:
                    # For dynamic assessments, fetch questions but exclude those assigned to fixed assessments
                    all_questions_for_topic = fetch_dynamic_questions_excluding_fixed(question_topic_identifier)
                    info(
                        f"Fetched {len(all_questions_for_topic)} questions for topic: "
                        f"{question_topic_identifier} (excluding fixed assessment questions)"
                    )

        # For in-progress sessions, get attempted questions from the session details
        # This ensures we don't show questions the user has already answered in this session
        if session_details.get("attempted_questions") and not retake:
            attempted_questions = session_details.get("attempted_questions", [])
            info(f"Found {len(attempted_questions)} attempted questions for in-progress session {session_code}")
        else:
            # Fallback to legacy method for backward compatibility
            attempted_questions = [] if retake else fetch_attempted_question_ids(question_topic_identifier, user_id)

        # For fixed assessments, we don't need to filter by final question IDs
        # For dynamic assessments, we need to handle final questions appropriately
        if is_fixed_assessment:
            # For fixed assessments, we only filter out attempted questions
            # and filter by difficulty if specified
            filtered_questions = []
            for question in all_questions_for_topic:
                # Skip attempted questions unless retake is allowed
                if question["que_id"] in attempted_questions and not retake:
                    continue

                # For fixed assessments, if difficulty is specified, filter by it
                # Otherwise, include all questions regardless of difficulty
                if difficulty != "all" and question["level"] != difficulty:
                    continue

                filtered_questions.append(question)
        else:
            # If it's a final quiz, all_questions_for_topic are already the selected final questions.
            # If it's mock, all_questions_for_topic are all questions for that base name.
            final_question_ids_for_topic = get_final_question_ids(question_topic_identifier) if not is_final else []

            filtered_questions = filter_questions(
                all_questions_for_topic,
                attempted_questions,
                # Only relevant for mock to exclude questions already in a final set
                final_question_ids_for_topic,
                is_final,
                difficulty,
                allow_retake=not is_final and retake,  # Retake only for mock
            )

        # Determine number of questions to return
        num_questions_to_return = 0

        if is_fixed_assessment:
            # For fixed assessments, return all filtered questions
            # This ensures we return the exact set of questions configured for this assessment
            num_questions_to_return = len(filtered_questions)
        elif is_final:
            # For final dynamic assessments, return all available non-attempted final questions
            num_questions_to_return = len(filtered_questions)
        else:  # Mock quiz with dynamic questions
            # Use assessment's total_questions instead of environment variable
            try:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor() as cur:
                        cur.execute(
                            "SELECT total_questions FROM assessments WHERE id = %s",
                            (assessment_id,),
                        )
                        result = cur.fetchone()
                        if result:
                            num_questions_to_return = result[0]
                        else:
                            # Fallback to environment variable if assessment not found
                            mock_q_count_env = os.getenv("MOCK_QUESTION_COUNT", "10")
                            num_questions_to_return = int(mock_q_count_env)
            except Exception as e:
                error(f"Error fetching assessment total_questions: {e}")
                # Fallback to environment variable
                mock_q_count_env = os.getenv("MOCK_QUESTION_COUNT", "10")
                try:
                    num_questions_to_return = int(mock_q_count_env)
                except ValueError:
                    num_questions_to_return = 10

        if not filtered_questions:
            error(f"No filtered questions available. Original questions: {len(all_questions_for_topic)}")
            return error_response(
                message=get_error_message(is_final, question_topic_identifier, difficulty),
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Sample questions if more are available than needed (especially for mock)
        if is_fixed_assessment:
            # For fixed assessments, don't sample - return all filtered questions in order
            selected_questions = filtered_questions
        elif len(filtered_questions) > num_questions_to_return:
            # For dynamic assessments with skills, try to get a balanced distribution from each skill
            if not is_fixed_assessment and skill_ids and len(skill_ids) > 1:
                # Group questions by skill
                questions_by_skill = {}
                for q in filtered_questions:
                    skill_id = q.get("skill_id")
                    if skill_id:
                        if skill_id not in questions_by_skill:
                            questions_by_skill[skill_id] = []
                        questions_by_skill[skill_id].append(q)

                # Calculate how many questions to take from each skill
                questions_per_skill = num_questions_to_return // len(questions_by_skill)
                remainder = num_questions_to_return % len(questions_by_skill)

                # Select questions from each skill
                selected_questions = []
                for skill_id, questions in questions_by_skill.items():
                    # Take extra question from skills until remainder is used up
                    skill_question_count = questions_per_skill + (1 if remainder > 0 else 0)
                    remainder -= 1 if remainder > 0 else 0

                    # If we have more questions than needed for this skill, sample randomly
                    if len(questions) > skill_question_count:
                        selected_questions.extend(random.sample(questions, skill_question_count))
                    else:
                        selected_questions.extend(questions)

                # If we still need more questions (due to some skills having fewer questions than allocated)
                if len(selected_questions) < num_questions_to_return:
                    # Get all questions that weren't selected
                    remaining_questions = [q for q in filtered_questions if q not in selected_questions]
                    # Sample from remaining questions to fill the gap
                    additional_needed = num_questions_to_return - len(selected_questions)
                    if remaining_questions and additional_needed > 0:
                        if len(remaining_questions) > additional_needed:
                            selected_questions.extend(random.sample(remaining_questions, additional_needed))
                        else:
                            selected_questions.extend(remaining_questions)
            else:
                # For single skill or non-skill-based assessments, just sample randomly
                selected_questions = random.sample(filtered_questions, num_questions_to_return)
        else:
            selected_questions = filtered_questions

        if not selected_questions:  # Double check after sampling
            raise_http_exception(
                status_code=404, detail=get_error_message(is_final, question_topic_identifier, difficulty)
            )

        # Get assessment description for user context
        assessment_description_for_user = get_assessment_description(session_details["assessment_id"])

        # Get total questions for this assessment
        total_questions = None
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                if is_fixed_assessment:
                    # For fixed assessments, count questions in assessment_questions
                    cur.execute(
                        "SELECT COUNT(*) FROM assessment_questions WHERE assessment_id = %s",
                        (session_details["assessment_id"],),
                    )
                    total_questions = cur.fetchone()[0]
                else:
                    # For dynamic assessments, use the assessment's total_questions field
                    cur.execute(
                        "SELECT total_questions FROM assessments WHERE id = %s",
                        (session_details["assessment_id"],),
                    )
                    result = cur.fetchone()
                    if result and result[0]:
                        total_questions = result[0]

        # Get attempted questions count
        attempted_questions_count = len(session_details.get("attempted_questions", []))

        # Get remaining time
        remaining_time_seconds = session_details.get("remaining_time_seconds", None)

        formatted_data = format_response(
            quiz_type,
            # Full assessment name like "MyQuiz_ts Mock Assessment"
            assessment_name_from_session,
            assessment_description_for_user or question_topic_identifier,  # User-facing topic
            selected_questions,
            remaining_time_seconds=remaining_time_seconds,
            attempted_questions_count=attempted_questions_count,
            total_questions=total_questions,
        )
        return success_response(data=formatted_data, message="Questions retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(
            f"Error fetching questions for session code {session_code}: {str(e)}",
            exc_info=True,
        )
        raise_http_exception(status_code=500, detail=f"Failed to load questions: {str(e)}")


def filter_questions(
    all_questions,
    attempted,
    final_ids_to_exclude_from_mock,
    is_final,
    difficulty,
    allow_retake=False,
):
    """Filter questions based on quiz type and difficulty."""
    if is_final:
        # For final quizzes, all_questions are already the selected final questions.
        # Filter out attempted ones.
        if difficulty != "all":
            # If a specific difficulty is requested, filter by it
            filtered_by_difficulty = get_questions_by_difficulty(all_questions, difficulty)
            return [q for q in filtered_by_difficulty if q["que_id"] not in attempted]
        else:
            # If all difficulties are requested, just filter out attempted ones
            return [q for q in all_questions if q["que_id"] not in attempted]

    # For mock quizzes
    if difficulty != "all":
        # If a specific difficulty is requested, filter by it
        questions_of_difficulty = get_questions_by_difficulty(all_questions, difficulty)
    else:
        # If all difficulties are requested, use all questions
        questions_of_difficulty = all_questions

    # Filter out attempted questions (if not retake) and questions already in a final set for this topic
    return [
        q
        for q in questions_of_difficulty
        if (allow_retake or q["que_id"] not in attempted) and q["que_id"] not in final_ids_to_exclude_from_mock
    ]


def get_error_message(is_final, quiz_name_base, difficulty):
    """Generate an appropriate error message."""
    if is_final:
        return f"No more final questions available for assessment: {quiz_name_base}, or you have attempted all."
    else:
        return (
            f"No new '{difficulty}' questions available for assessment: {quiz_name_base}. "
            "This could be because: 1) All questions have been attempted, "
            "2) All questions are assigned to fixed assessments, "
            "or 3) No questions exist for this difficulty level. Try another difficulty or create more questions."
        )


def format_response(
    quiz_type,
    assessment_full_name,
    user_facing_topic,
    selected_questions,
    remaining_time_seconds=None,
    attempted_questions_count=None,
    total_questions=None,
):
    """Format response JSON."""
    response = {
        "quiz_type": quiz_type,
        # e.g. "MyAssessment_timestamp Mock Assessment"
        "quiz_name": assessment_full_name,
        "topic": user_facing_topic,  # Skill description or similar for user context
        "question": [
            {
                "que_id": q["que_id"],
                "question": q["question"],
                "options": q["options"],
                "level": q["level"],
            }
            for q in selected_questions
        ],
    }

    # Add session progress information if available
    if remaining_time_seconds is not None:
        response["remaining_time_seconds"] = remaining_time_seconds

    if attempted_questions_count is not None:
        response["attempted_questions_count"] = attempted_questions_count

    if total_questions is not None:
        response["total_questions"] = total_questions
        response["questions_left"] = max(0, total_questions - (attempted_questions_count or 0))

    return response


@api_router.post("/check_and_save_answer")
def check_and_save_answer_endpoint(
    # Contains session_code now
    request: AnswerRequest,
    _: None = Depends(rate_limiter),
):
    """
    Checks and saves the user's answer for a specific quiz question using session_code.
    """
    # The internal check_and_save_answer function now handles fetching session details
    result = check_and_save_answer(
        user_id=request.user_id,
        question_id=request.question_id,
        answer=request.answer,
        session_code=request.session_code,
        time_taken=request.time_taken,
    )
    return result


def _validate_submit_session_request(session_code_input: str, user_id: str) -> str:
    """Validate submit session request parameters."""
    if not session_code_input:
        raise_http_exception(status_code=400, detail="Session code is required.")
    if not user_id:
        raise_http_exception(status_code=400, detail="User ID is required.")

    session_code = validate_session_code_format(session_code_input)
    debug(f"Decoded session code: '{session_code}'")
    return session_code


def _get_session_details_for_submit(session_code: str) -> dict:
    """Get session details and validate they exist."""
    session_details = get_session_and_assessment_details_by_code(session_code)
    if not session_details:
        debug(f"Session not found for code: '{session_code}'")
        raise_http_exception(status_code=404, detail="Invalid or expired session code.")

    debug(
        f"Session details: status='{session_details['session_status']}', "
        f"session_id={session_details.get('session_id')}"
    )
    return session_details


def _handle_completed_session(session_details: dict):
    """Handle already completed session."""
    debug("Session already completed, returning existing data")

    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                "SELECT score, completed_at FROM sessions WHERE id = %s",
                (session_details["session_id"],),
            )
            result = cur.fetchone()
            if result:
                data = {
                    "session_id": session_details["session_id"],
                    "obtained_score": result["score"] or 0,
                    "status": "completed",
                    "message": "Session was already completed",
                }
                hashed_data = hash_ids_in_response(data)
                return success_response(data=hashed_data, message="Session was already completed")

    return success_response(
        data={"status": "completed", "message": "Session was already completed"},
        message="Session was already completed",
    )


def _handle_expired_session(session_details: dict):
    """Handle expired session by completing it."""
    debug("Session expired, but attempting to complete anyway")

    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                """UPDATE sessions
                   SET status = 'completed', completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                   WHERE id = %s
                   RETURNING id""",
                (session_details["session_id"],),
            )
            result = cur.fetchone()
            if result:
                conn.commit()
                debug(f"Successfully completed expired session {session_details['session_id']}")
                return success_response(
                    data={"status": "completed", "message": "Expired session completed"},
                    message="Session completed successfully",
                )


def _validate_session_user(session_details: dict, user_id: str):
    """Validate that user matches the session."""
    internal_user_id = get_or_create_user(user_id)
    debug(f"User validation: internal_user_id={internal_user_id}, session_user_id={session_details.get('user_id')}")

    if session_details["user_id"] != internal_user_id:
        debug(
            f"User mismatch: session belongs to user_id={session_details['user_id']}, "
            f"but request from user_id={internal_user_id}"
        )
        raise_http_exception(status_code=403, detail="User does not match session.")


def _calculate_session_scores(session_details: dict) -> tuple[float, float, str]:
    """Calculate obtained score, total possible score, and performance level."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            # Calculate obtained score from user_answers
            cur.execute(
                "SELECT COALESCE(SUM(score), 0) as obtained_score FROM user_answers WHERE session_id = %s",
                (session_details["session_id"],),
            )
            score_result = cur.fetchone()
            obtained_score = score_result["obtained_score"] if score_result else 0

            # Calculate correct total possible score based on assessment mode
            assessment_id = session_details["assessment_id"]
            session_id = session_details["session_id"]
            total_possible_score = calculate_total_score_for_assessment(assessment_id, session_id)

            # Calculate performance level with correct total score
            performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_id)

            return obtained_score, total_possible_score, performance_level


def _complete_session_in_db(session_details: dict, obtained_score: float):
    """Complete the session in database."""
    with psycopg2.connect(**DATABASE_CONFIG) as conn:
        with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
            cur.execute(
                """UPDATE sessions
                   SET status = 'completed',
                       score = %s,
                       completed_at = CURRENT_TIMESTAMP AT TIME ZONE 'UTC'
                   WHERE id = %s AND status = 'in_progress'
                   RETURNING id""",
                (obtained_score, session_details["session_id"]),
            )

            updated_session = cur.fetchone()
            if not updated_session:
                raise_http_exception(status_code=400, detail="Session could not be completed.")

            conn.commit()


@api_router.post("/submit_session")
def submit_session(request: SessionSubmissionRequest, _: None = Depends(rate_limiter)):
    """
    Submit a quiz session, marking it as completed and calculating final score.
    """
    try:
        session_code_input = request.session_code
        user_id = request.user_id

        debug(f"Submit session request: session_code='{session_code_input}', user_id='{user_id}'")

        # Validate request parameters
        session_code = _validate_submit_session_request(session_code_input, user_id)

        # Get session details
        session_details = _get_session_details_for_submit(session_code)

        # Handle different session statuses
        current_status = session_details["session_status"]

        if current_status == "completed":
            return _handle_completed_session(session_details)
        elif current_status == "expired":
            return _handle_expired_session(session_details)
        elif current_status != "in_progress":
            debug(f"Session not in progress. Current status: {current_status}")
            raise_http_exception(
                status_code=400, detail=f"Session is not in progress. Current status: {current_status}"
            )

        # Validate user matches session
        _validate_session_user(session_details, user_id)

        # Calculate scores
        obtained_score, total_possible_score, performance_level = _calculate_session_scores(session_details)

        # Complete session in database
        _complete_session_in_db(session_details, obtained_score)

        # Calculate percentage and prepare response
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        data = {
            "session_id": session_details["session_id"],
            "obtained_score": obtained_score,
            "total_possible_score": total_possible_score,
            "percentage": round(percentage, 2),
            "performance_level": performance_level,
            "status": "completed",
        }
        hashed_data = hash_ids_in_response(data)
        return success_response(data=hashed_data, message="Session submitted successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error submitting session {session_code_input}: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail=f"Error submitting session: {str(e)}")


@api_router.get("/get_progress")
def get_progress(
    user_id: str = Query(...),  # External user_id
    assessment_name: str = Query(...),  # Full assessment name, e.g. "MyQuiz_timestamp Mock Assessment"
    # quiz_type is implicitly defined by assessment_name containing "Mock" or "Final"
):
    """
    Retrieves the progress of a user on a specific assessment.
    The `assessment_name` should be the full name like "Topic_timestamp Assessment".
    """
    try:
        quiz_type_str = "assessment"  # Always use 'assessment' type

        # Base assessment name (topic for user_assessment table)
        assessment_base_name = assessment_name.replace(" Assessment", "")

        # Try to get progress from new schema first (user_answers via sessions)
        try:
            internal_user_id = get_or_create_user(user_id)

            # Find assessment_id from assessment_name
            db_assessment_id = None
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                    cur.execute("SELECT id FROM assessments WHERE name = %s", (assessment_name,))
                    assessment_row = cur.fetchone()
                    if assessment_row:
                        db_assessment_id = assessment_row["id"]

            if not db_assessment_id:
                warning(f"No assessment found with name {assessment_name} for progress.")
                # Fall through to legacy, or raise error. Let's fall through for now.

            if db_assessment_id:
                with psycopg2.connect(**DATABASE_CONFIG) as conn:
                    with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                        # Get the latest session for this user and assessment
                        cur.execute(
                            """SELECT s.id FROM sessions s
                               WHERE s.user_id = %s AND s.assessment_id = %s
                               ORDER BY s.created_at DESC LIMIT 1""",
                            (internal_user_id, db_assessment_id),
                        )
                        session_result = cur.fetchone()

                        if session_result:
                            session_id = session_result["id"]
                            cur.execute(
                                """SELECT ua.*, q.question, q.options, q.answer as correct_answer_key, q.level
                                   FROM user_answers ua
                                   JOIN questions q ON ua.question_id = q.que_id
                                   WHERE ua.session_id = %s
                                   ORDER BY ua.created_at""",
                                (session_id,),
                            )
                            answers = cur.fetchall()

                            if answers:
                                progress_data = []
                                total_score_val = 0

                                for answer_row in answers:
                                    result_text = "Correct" if answer_row["is_correct"] else "Incorrect"
                                    if answer_row["user_answer"] and answer_row["user_answer"].lower() == "timeout":
                                        result_text = "Timeout"

                                    progress_data.append(
                                        {
                                            "question": answer_row["question"],
                                            "options": answer_row["options"],
                                            "correct_answer": answer_row[
                                                "correct_answer_key"
                                            ],  # This is the key 'a', 'b' etc.
                                            "user_answer": answer_row["user_answer"] or "None",
                                            "result": result_text,
                                            "score": answer_row["score"],
                                        }
                                    )
                                    total_score_val += answer_row["score"]

                                user_level_val = get_performance_level_with_correct_total(
                                    total_score_val, db_assessment_id, session_id
                                )

                                total_possible_score = calculate_total_score_for_assessment(
                                    db_assessment_id, session_id
                                )
                                percentage = (
                                    (total_score_val / total_possible_score * 100) if total_possible_score > 0 else 0
                                )

                                return success_response(
                                    data={
                                        "obtained_score": total_score_val,
                                        "total_possible_score": total_possible_score,
                                        "percentage": round(percentage, 2),
                                        "user_level": user_level_val,
                                        "progress_report": progress_data,
                                    },
                                    message="Progress retrieved successfully",
                                )
        except Exception as e:
            warning(
                f"Error getting progress from new schema for {user_id} on {assessment_name}, falling back: {e}",
                exc_info=True,
            )

        # Fall back to old schema (user_assessment table)
        # fetch_user_progress expects assessment_base_name and quiz_type_str
        info(
            "Falling back to legacy progress for user %s, assessment base %s, type %s",
            user_id,
            assessment_base_name,
            quiz_type_str,
        )

        progress_data_legacy = fetch_user_progress(assessment_base_name, user_id, quiz_type_str)
        if not progress_data_legacy:  # If legacy also returns empty, then no progress.
            return success_response(
                data={
                    "total_score": 0,
                    "user_level": "N/A",
                    "progress_report": [],
                },
                message="No progress found",
            )

        logging.debug(f"Legacy Progress Data: {progress_data_legacy}")
        total_score_legacy = sum(item["score"] for item in progress_data_legacy)
        user_level_legacy = get_performance_level_util(total_score_legacy)  # Simpler version
        return success_response(
            data={
                "total_score": total_score_legacy,
                "user_level": user_level_legacy,
                "progress_report": progress_data_legacy,
            },
            message="Progress retrieved successfully",
        )
    except Exception as e:
        logging.error(
            f"Error in get_progress for {user_id} on {assessment_name}: {str(e)}",
            exc_info=True,
        )
        raise_http_exception(status_code=500, detail=f"Error retrieving progress: {str(e)}")


@api_router.post("/check_user")
def check_user(request: UserCheckRequest):
    """
    Check if a user is in the list of allowed users.
    """
    if request.user_id in ALLOWED_USERS:
        return success_response(data={"status": "true"}, message="User is allowed")
    return success_response(data={"status": "false"}, message="User is not in allowed list")


@api_router.get("/get_question_counts")
def get_question_counts():
    """
    Fetch question counts for different difficulty levels for final assessments.
    """
    n = int(os.getenv("FINAL_QUESTION_COUNT", "20"))
    easy, intermediate, advanced = divide_number(n)
    return success_response(
        data={"easy": easy, "intermediate": intermediate, "advanced": advanced},
        message="Question counts retrieved successfully",
    )


@api_router.get("/health")
async def health_check():
    """Health check endpoint for monitoring"""
    try:
        # Check database connection
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor() as cur:
                cur.execute("SELECT 1")
                result = cur.fetchone()

        if result and result[0] == 1:
            return success_response(
                data={"status": "healthy", "database": "connected"},
                message="Health check passed",
            )
        else:
            error("Health check: DB query did not return 1")
            return JSONResponse(
                status_code=503,
                content=error_response(
                    message="Health check failed",
                    code=503,
                    error_type="ServiceUnavailable",
                    details={"database": "error", "detail": "DB query failed"},
                ),
            )
    except Exception as e:
        error("Health check failed", exception=e)
        return JSONResponse(
            status_code=503,
            content=error_response(
                message="Health check failed",
                code=503,
                error_type="ServiceUnavailable",
                details={"error": str(e)},
            ),
        )


@api_router.post("/admin/final-questions")
async def add_final_questions(request: FinalQuestionsRequest, _: None = Depends(rate_limiter)):
    """Add questions to a final assessment or a fixed assessment"""
    try:
        # Check if this is for a fixed assessment
        if request.assessment_id:
            # This is for a fixed assessment
            # First, validate the assessment exists and is a fixed assessment
            with psycopg2.connect(**DATABASE_CONFIG) as conn:
                with conn.cursor() as cur:
                    cur.execute(
                        "SELECT id, question_selection_mode FROM assessments WHERE id = %s",
                        (request.assessment_id,),
                    )
                    assessment = cur.fetchone()
                    if not assessment:
                        raise_http_exception(
                            status_code=404, detail=f"Assessment with ID {request.assessment_id} not found"
                        )

                    if assessment[1] != "fixed":
                        raise_http_exception(
                            status_code=400,
                            detail="Questions can only be assigned to assessments with 'fixed' question selection mode",
                        )

                    # Get the skill IDs associated with this assessment
                    cur.execute(
                        "SELECT skill_id FROM assessment_skills WHERE assessment_id = %s",
                        (request.assessment_id,),
                    )
                    results = cur.fetchall()
                    if not results:
                        raise HTTPException(
                            status_code=400,
                            detail="Assessment has no associated skills",
                        )

                    # Extract skill IDs from the results
                    skill_ids = []
                    for row in results:
                        skill_id = row[0]
                        skill_ids.append(skill_id)

                    # Check if all question IDs exist and belong to the assessment's skills
                    # Use parameterized queries for both question IDs and skill IDs
                    q_placeholders = ",".join(["%s"] * len(request.question_ids))
                    s_placeholders = ",".join(["%s"] * len(skill_ids))

                    query = f"""
                        SELECT COUNT(*)
                        FROM questions
                        WHERE que_id IN ({q_placeholders})
                        AND skill_id IN ({s_placeholders})
                    """
                    # Combine both lists of parameters
                    params = request.question_ids + skill_ids
                    cur.execute(query, params)
                    count = cur.fetchone()[0]
                    if count != len(request.question_ids):
                        raise HTTPException(
                            status_code=400,
                            detail="Some question IDs do not exist or are not associated with the assessment's skills",
                        )

                    # Check if the questions meet the minimum difficulty requirements
                    cur.execute(
                        f"""
                        SELECT level, COUNT(*)
                        FROM questions
                        WHERE que_id IN ({q_placeholders})
                        GROUP BY level
                        """,
                        request.question_ids,
                    )
                    level_counts = dict(cur.fetchall())

                    # Get minimum required counts from environment variables
                    min_easy = int(os.getenv("EASY_QUESTIONS_COUNT", "6"))
                    min_intermediate = int(os.getenv("INTERMEDIATE_QUESTIONS_COUNT", "6"))
                    min_advanced = int(os.getenv("ADVANCED_QUESTIONS_COUNT", "8"))

                    selected_easy = level_counts.get("easy", 0)
                    selected_intermediate = level_counts.get("intermediate", 0)
                    selected_advanced = level_counts.get("advanced", 0)

                    # Validate minimum requirements (allow more than minimum)
                    if (
                        selected_easy < min_easy
                        or selected_intermediate < min_intermediate
                        or selected_advanced < min_advanced
                    ):
                        raise HTTPException(
                            status_code=400,
                            detail=(
                                f"Selection must contain at least "
                                f"{min_easy} easy, {min_intermediate} intermediate, "
                                f"and {min_advanced} advanced questions (you can add more). Found: "
                                f"easy: {selected_easy}, intermediate: {selected_intermediate}, "
                                f"advanced: {selected_advanced}"
                            ),
                        )

                    # Clear existing assigned questions for this assessment
                    cur.execute(
                        "DELETE FROM assessment_questions WHERE assessment_id = %s",
                        (request.assessment_id,),
                    )

                    # Insert the new question assignments
                    for question_id in request.question_ids:
                        cur.execute(
                            """
                            INSERT INTO assessment_questions (assessment_id, question_id)
                            VALUES (%s, %s)
                            """,
                            (request.assessment_id, question_id),
                        )

                    conn.commit()

                    response_data = {
                        "assessment_id": request.assessment_id,
                        "count": len(request.question_ids),
                        "difficulty_counts": {
                            "easy": selected_easy,
                            "intermediate": selected_intermediate,
                            "advanced": selected_advanced,
                        },
                    }
                    hashed_data = hash_ids_in_response(response_data)
                    return success_response(
                        data=hashed_data,
                        message=(
                            f"Successfully assigned {len(request.question_ids)} questions to fixed assessment "
                            f"(minimum requirements: {min_easy} easy, {min_intermediate} intermediate, "
                            f"{min_advanced} advanced)"
                        ),
                    )
        else:
            # This is for a regular final assessment
            # Insert the final questions
            result = insert_final_questions_db(request.question_ids)

            if result:
                return success_response(
                    data={"count": len(request.question_ids)},
                    message="Final questions added successfully",
                )
            else:
                raise_http_exception(status_code=400, detail="Failed to add final questions")
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error adding final questions: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error adding final questions: {str(e)}")


def _build_session_filter_clauses(status_filter: Optional[str]) -> tuple[str, str]:
    """Build WHERE clauses for session filtering."""
    if status_filter == "pending":
        clause = "WHERE (s.status = 'pending' OR s.status = 'created' OR s.status IS NULL)"
        return clause, clause
    elif status_filter == "completed":
        clause = "WHERE (s.status = 'completed' OR s.status = 'finished')"
        return clause, clause
    else:
        # If status_filter is 'all' or None, no WHERE clause needed
        return "", ""


def _get_sessions_count(cur, count_where_clause: str) -> int:
    """Get total count of sessions based on filter."""
    count_query = f"""
        SELECT COUNT(*)
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
        {count_where_clause}
    """
    cur.execute(count_query)
    return cur.fetchone()[0]


def _get_sessions_data(cur, where_clause: str, limit: int, offset: int) -> list:
    """Get paginated sessions data."""
    main_query = f"""
        SELECT s.id, s.code as session_code, u.external_id as username,
               s.assessment_id, s.score, s.status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name,
               CASE
                   WHEN a.question_selection_mode = 'fixed' THEN
                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                   ELSE
                       (SELECT COUNT(DISTINCT ua.question_id)
                        FROM user_answers ua
                        WHERE ua.session_id = s.id)
               END as total_questions
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
        {where_clause}
        ORDER BY s.created_at DESC
        LIMIT %s OFFSET %s
    """
    cur.execute(main_query, (limit, offset))
    return [dict(row) for row in cur.fetchall()]


@api_router.get("/admin/sessions")
async def get_sessions(
    limit: int = Query(3, ge=1, le=100),
    offset: int = Query(0, ge=0),
    status_filter: Optional[str] = Query(
        None, description="Filter by session status: 'pending', 'completed', or 'all'"
    ),
):
    """
    Get sessions with pagination and optional status filtering

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
        status_filter: Filter by session status ('pending', 'completed', or 'all')
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Build WHERE clauses based on status filter
                where_clause, count_where_clause = _build_session_filter_clauses(status_filter)

                # Get total count
                total = _get_sessions_count(cur, count_where_clause)

                # Get paginated sessions data
                sessions = _get_sessions_data(cur, where_clause, limit, offset)

                # Transform response to include hashed IDs
                hashed_sessions = hash_ids_in_response(sessions)

                # Return paginated response
                return paginated_response(
                    data=hashed_sessions,
                    total=total,
                    limit=limit,
                    offset=offset,
                    message="Sessions retrieved successfully",
                    additional_data={"status_filter": status_filter or "all"},
                )
    except Exception as e:
        error(f"Error getting sessions: {str(e)}")
        return error_response(
            message=f"Error getting sessions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/user/{email}/sessions")
async def get_user_sessions_by_email(email: str):
    """Get sessions for a specific user identified by email"""
    try:

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, get the user's internal ID
                cur.execute(
                    """
                    SELECT id FROM users WHERE email = %s
                    """,
                    (email,),
                )
                user_result = cur.fetchone()
                if not user_result:
                    warning(f"User with email {email} not found in database")
                    return success_response(
                        data={"sessions": []},
                        message=f"No user found with email {email}",
                    )

                user_id = user_result["id"]

                # Now get all sessions for this user
                cur.execute(
                    """
                    SELECT s.id, s.code as session_code, u.external_id as username,
                           s.assessment_id, s.score, s.status,
                           s.created_at, s.completed_at, s.started_at,
                           a.name as assessment_name, a.question_selection_mode,
                           CASE
                               WHEN a.question_selection_mode = 'fixed' THEN
                                   (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                               ELSE
                                   (SELECT COUNT(DISTINCT ua.question_id)
                                    FROM user_answers ua
                                    WHERE ua.session_id = s.id)
                           END as total_questions
                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    JOIN users u ON s.user_id = u.id
                    WHERE s.user_id = %s
                    ORDER BY s.created_at DESC
                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]
                hashed_sessions = hash_ids_in_response(sessions)

                return success_response(
                    data={
                        "sessions": hashed_sessions,
                        "total": len(sessions),
                        "count": len(sessions),
                    },
                    message="User sessions retrieved successfully",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user sessions: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error getting user sessions: {str(e)}")


# --- HELPER FUNCTION ---
def find_or_create_user_by_details(cur, display_name: str, email: str, external_id: str):
    """
    Finds a user by their unique email or creates a new one if not found.
    This is a robust "upsert" (update/insert) operation.

    Returns:
        int: The internal database ID of the user (either existing or newly created).
    """
    # First, try to find the user by their unique email.
    cur.execute("SELECT id FROM users WHERE email = %s", (email,))
    user = cur.fetchone()
    if user:
        return user["id"]

    # If the user doesn't exist, create a new one with all required fields.
    cur.execute(
        """
        INSERT INTO users (display_name, email, external_id, created_at, updated_at)
        VALUES (%s, %s, %s, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
        RETURNING id
        """,
        (display_name, email, external_id),
    )
    new_user = cur.fetchone()
    if not new_user:
        # This would indicate a serious database issue.
        raise Exception(f"Failed to create user with email {email}")
    return new_user["id"]


def _validate_session_request(request_data: dict) -> tuple[int, list[str]]:
    """Validate session generation request and return assessment_id and user_identifiers."""
    assessment_id_str = request_data.get("assessment_id")
    usernames_str = request_data.get("usernames", "")

    if not assessment_id_str or not usernames_str:
        raise_http_exception(status_code=400, detail="Assessment ID and usernames are required.")

    try:
        assessment_id = int(assessment_id_str)
    except (ValueError, TypeError):
        raise_http_exception(status_code=400, detail="Assessment ID must be a valid number.")

    user_identifiers = [identifier.strip() for identifier in usernames_str.split(",") if identifier.strip()]

    if not user_identifiers:
        raise_http_exception(status_code=400, detail="No valid usernames provided.")

    return assessment_id, user_identifiers


def _validate_assessment_exists(cur, assessment_id: int):
    """Validate that assessment exists in database."""
    cur.execute("SELECT id, name FROM assessments WHERE id = %s", (assessment_id,))
    if not cur.fetchone():
        raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found.")


def _process_user_identifier(identifier: str, default_email_domain: str) -> tuple[str, str, str]:
    """Process user identifier and return display_name, email, and external_id."""
    if "@" in identifier:
        email = identifier.lower()
        display_name = email.split("@")[0]
    else:
        display_name = identifier
        email = f"{display_name.lower()}@{default_email_domain}"

    external_id = display_name
    return display_name, email, external_id


def _generate_unique_session_code(cur) -> str:
    """Generate a unique session code."""
    for _ in range(10):  # Max 10 attempts
        session_code = str(random.randint(100000, 999999)).zfill(6)
        cur.execute("SELECT id FROM sessions WHERE code = %s", (session_code,))
        if not cur.fetchone():
            return session_code

    raise Exception("Could not generate a unique session code after 10 attempts.")


def _create_session_in_db(cur, session_code: str, user_internal_id: int, assessment_id: int) -> int:
    """Create session in database and return session ID."""
    cur.execute(
        """INSERT INTO sessions (code, user_id, assessment_id, status, created_at)
           VALUES (%s, %s, %s, %s, CURRENT_TIMESTAMP)
           RETURNING id""",
        (session_code, user_internal_id, assessment_id, "pending"),
    )
    return cur.fetchone()["id"]


def _process_single_user_session(cur, identifier: str, assessment_id: int, default_email_domain: str) -> dict:
    """Process a single user session creation."""
    # Process the identifier
    display_name, email, external_id = _process_user_identifier(identifier, default_email_domain)

    # Find or create the user in the database
    user_internal_id = find_or_create_user_by_details(
        cur,
        display_name=display_name,
        email=email,
        external_id=external_id,
    )

    # Generate a unique session code
    session_code = _generate_unique_session_code(cur)

    # Insert the new session into the database
    session_db_id = _create_session_in_db(cur, session_code, user_internal_id, assessment_id)

    return {
        "id": session_db_id,
        "username": display_name,
        "sessionCode": session_code,
        "sessionDbId": session_db_id,
    }


# --- MAIN ENDPOINT ---
@api_router.post("/admin/sessions")
async def generate_sessions(request_data: dict, _: None = Depends(rate_limiter)):
    """
    Generates assessment session codes for a list of users.
    It intelligently handles both usernames and email addresses as input.
    """
    try:
        # Validate request
        assessment_id, user_identifiers = _validate_session_request(request_data)

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Validate assessment exists
                _validate_assessment_exists(cur, assessment_id)

                created_sessions = []
                failed_sessions = []
                default_email_domain = os.getenv("DEFAULT_EMAIL_DOMAIN", "example.com")

                # Process each user identifier
                for identifier in user_identifiers:
                    try:
                        session_data = _process_single_user_session(
                            cur, identifier, assessment_id, default_email_domain
                        )
                        created_sessions.append(session_data)

                    except Exception as user_error:
                        error(f"Error creating session for identifier '{identifier}': {str(user_error)}")
                        failed_sessions.append(f"{identifier} (error: {str(user_error)})")

                conn.commit()

                # Prepare and return the final response
                if not created_sessions and failed_sessions:
                    raise_http_exception(
                        status_code=500, detail=f"Failed to create any sessions. Errors: {', '.join(failed_sessions)}"
                    )

                response_data = {"sessions": created_sessions}
                if failed_sessions:
                    response_data["warnings"] = f"Failed to create sessions for: {', '.join(failed_sessions)}"

                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message=f"Generated {len(created_sessions)} session codes successfully.",
                )

    except HTTPException as http_exc:
        raise http_exc
    except Exception as e:
        error(f"Unexpected error in generate_sessions: {str(e)}", exc_info=True)
        raise_http_exception(status_code=500, detail="An unexpected error occurred while generating sessions.")


@api_router.post("/admin/reports")
async def generate_report(request: ReportRequest, _: None = Depends(rate_limiter)):
    """Generate reports based on user or topic"""
    try:
        if request.report_type == "user_wise" and request.user_name:
            base_report, score_report = assessment_report_by_user(request.user_name, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated user-wise report for {request.user_name}",
            )

        elif request.report_type == "topic_wise" and request.report_topic:
            base_report, score_report = assessment_report_by_topic(request.report_topic, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": base_report,
                    "score_report": score_report,
                },
                message=f"Generated topic-wise report for {request.report_topic}",
            )

        elif request.report_type == "assessment_wise" and request.assessment_base_name:
            result = assessment_report_with_question_stats(request.assessment_base_name, request.quiz_type or "mock")
            return success_response(
                data={
                    "base_report": result["base_report"],
                    "score_report": result["score_report"],
                },
                message=f"Generated assessment-wise report for {request.assessment_base_name}",
            )

        else:
            return error_response(
                message="Invalid report type or missing required parameters",
                code=status.HTTP_400_BAD_REQUEST,
                error_type="BadRequest",
            )

    except Exception as e:
        error(f"Error generating report: {str(e)}")
        return error_response(
            message=f"Error generating report: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/admin/assessments")
async def get_assessments(
    limit: int = Query(7, ge=1, le=100),
    offset: int = Query(0, ge=0),
):
    """
    Get all assessments with pagination

    Args:
        limit: Maximum number of items per page (default: 3)
        offset: Starting position (default: 0)
    """
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get total count first
                cur.execute("SELECT COUNT(*) FROM assessments")
                total = cur.fetchone()[0]

                # Get paginated results
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    ORDER BY created_at DESC
                    LIMIT %s OFFSET %s
                    """,
                    (limit, offset),
                )
                assessments = [dict(row) for row in cur.fetchall()]

                # Get skill IDs for each assessment
                for assessment in assessments:
                    cur.execute(
                        """
                        SELECT skill_id FROM assessment_skills
                        WHERE assessment_id = %s
                        """,
                        (assessment["id"],),
                    )
                    skill_id_rows = cur.fetchall()

                    # Extract all skill IDs into a list
                    skill_ids = []
                    for row in skill_id_rows:
                        skill_id = row[0]
                        # Check if it's a JSON string (unlikely based on schema, but being cautious)
                        if isinstance(skill_id, str) and skill_id.startswith("[") and skill_id.endswith("]"):
                            try:
                                json_skill_ids = json.loads(skill_id)
                                skill_ids.extend(json_skill_ids)
                            except json.JSONDecodeError:
                                skill_ids.append(skill_id)
                        else:
                            # It's a single skill ID (integer)
                            skill_ids.append(skill_id)

                    assessment["skill_ids"] = skill_ids

                # Transform response to include hashed IDs
                hashed_assessments = hash_ids_in_response(assessments)

                # Return paginated response
                return paginated_response(
                    data=hashed_assessments,
                    total=total,
                    limit=limit,
                    offset=offset,
                    message="Assessments retrieved successfully",
                )
    except Exception as e:
        error(f"Error fetching assessments: {str(e)}")
        return error_response(
            message=f"Error fetching assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/admin/assessment/{assessment_id}")
async def get_assessment(
    assessment_id: str,
    include_questions: bool = Query(False),
    include_answers: bool = Query(False),
):
    """
    Get a single assessment without questions by default.
    For security, questions and answers are excluded unless explicitly requested.
    """
    try:
        # Try to decode if it's a hash, otherwise treat as integer
        actual_id = assessment_id
        if not assessment_id.isdigit():
            decoded_id = decode_assessment_id(assessment_id)
            if decoded_id is not None:
                actual_id = decoded_id
            else:
                return error_response(
                    message=f"Invalid assessment ID format: {assessment_id}",
                    code=status.HTTP_400_BAD_REQUEST,
                    error_type="BadRequest",
                )
        else:
            actual_id = int(assessment_id)

        # For security, always exclude questions by default
        # Only include if explicitly requested and authorized
        safe_include_questions = False  # Always False for security
        safe_include_answers = False  # Always False for security

        # Use the new function from questions_logging
        assessment_dict = get_assessment_by_id(
            actual_id,
            include_questions=safe_include_questions,
            include_answers=safe_include_answers,
        )

        if not assessment_dict:
            return error_response(
                message=f"Assessment with ID {actual_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        # Transform response to include hashed IDs
        hashed_assessment = hash_ids_in_response(assessment_dict)

        return success_response(
            data=hashed_assessment,
            message="Assessment retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/admin/assessment-questions/{assessment_id}")
async def get_assessment_questions(assessment_id: int):
    """Get all available questions for an assessment based on its associated skills"""
    try:
        # Use the new function from questions_logging
        result = get_assessment_questions_by_id(assessment_id)

        if result is None:
            return error_response(
                message=f"Assessment with ID {assessment_id} not found",
                code=status.HTTP_404_NOT_FOUND,
                error_type="NotFound",
            )

        return success_response(
            data=result,
            message="Assessment questions retrieved successfully",
        )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching questions for assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching questions for assessment: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/admin/fixed-assessment-questions/{assessment_id}")
async def get_fixed_assessment_questions(assessment_id: int):
    """Get the assigned questions for a fixed assessment"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First check if the assessment exists and is a fixed assessment
                cur.execute(
                    "SELECT id, name, question_selection_mode FROM assessments WHERE id = %s",
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    return error_response(
                        message=f"Assessment with ID {assessment_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                if assessment["question_selection_mode"] != "fixed":
                    return error_response(
                        message=f"Assessment with ID {assessment_id} is not a fixed assessment",
                        code=status.HTTP_400_BAD_REQUEST,
                        error_type="BadRequest",
                    )

                # Get the assigned questions for this assessment
                cur.execute(
                    "SELECT question_id FROM assessment_questions WHERE assessment_id = %s ORDER BY id",
                    (assessment_id,),
                )
                results = cur.fetchall()

                # Extract question IDs
                question_ids = [row[0] for row in results]

                response_data = {
                    "assessment_id": assessment_id,
                    "assessment_name": assessment["name"],
                    "question_ids": question_ids,
                }

                return success_response(
                    data=response_data,
                    message=f"Retrieved {len(question_ids)} fixed questions for assessment '{assessment['name']}'",
                )

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching fixed assessment questions for assessment {assessment_id}: {str(e)}")
        return error_response(
            message=f"Error fetching fixed assessment questions: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.post("/admin/generate-link")
async def generate_quiz_link(request_data: dict, _: None = Depends(rate_limiter)):
    """Generate a shareable link for users to take an assessment"""
    try:
        assessment_id = request_data.get("assessment_id")

        if not assessment_id:
            raise_http_exception(status_code=400, detail="Assessment ID is required")

        # Verify the assessment exists
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute("SELECT id, name FROM assessments WHERE id = %s", (assessment_id,))
                assessment = cur.fetchone()

                if not assessment:
                    raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found")

        frontend_url = os.getenv("FRONTEND_URL", "http://localhost:5173")

        # Generate a unique 6-digit session code
        session_code = str(random.randint(100000, 999999)).zfill(6)
        hashed_session_code = encode_session_code(session_code)
        quiz_link = f"{frontend_url}/quiz/{hashed_session_code}"

        response_data = {
            "link": quiz_link,
            "assessment_id": assessment_id,
            "assessment_name": assessment["name"],
            "session_code": session_code,
            "hashed_session_code": hashed_session_code,
        }
        hashed_data = hash_ids_in_response(response_data)
        return success_response(data=hashed_data, message="Quiz link generated successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error generating quiz link: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error generating quiz link: {str(e)}")


@api_router.get("/admin/assessments/{assessment_id}")
async def get_single_assessment(assessment_id: int):
    """Get a single assessment by ID for the quiz link page"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT id, name, description, is_final, total_questions,
                           question_selection_mode, composition, duration_minutes, created_at
                    FROM assessments
                    WHERE id = %s
                    """,
                    (assessment_id,),
                )
                assessment = cur.fetchone()

                if not assessment:
                    raise_http_exception(status_code=404, detail=f"Assessment with ID {assessment_id} not found")

                return dict(assessment)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching assessment {assessment_id}: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error fetching assessment: {str(e)}")


def _get_session_query() -> str:
    """Get the base session query."""
    return """
        SELECT s.id, s.code as session_code, u.external_id as username,
               s.assessment_id, s.score, s.status,
               s.created_at, s.completed_at, s.started_at,
               a.name as assessment_name,
               CASE
                   WHEN a.question_selection_mode = 'fixed' THEN
                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                   ELSE
                       (SELECT COUNT(DISTINCT ua.question_id)
                        FROM user_answers ua
                        WHERE ua.session_id = s.id)
               END as total_questions
        FROM sessions s
        JOIN assessments a ON s.assessment_id = a.id
        JOIN users u ON s.user_id = u.id
    """


def _get_session_by_code(cur, session_id: str) -> dict:
    """Get session by 6-digit session code."""
    info(f"Using 6-digit session code: {session_id}")
    query = _get_session_query() + "WHERE s.code = %s"
    cur.execute(query, (session_id,))
    session = cur.fetchone()
    return dict(session) if session else None


def _get_session_by_numeric_id(cur, session_id: str) -> dict:
    """Get session by numeric ID."""
    actual_id = int(session_id)
    info(f"Using numeric session ID: {actual_id}")
    query = _get_session_query() + "WHERE s.id = %s"
    cur.execute(query, (actual_id,))
    session = cur.fetchone()
    return dict(session) if session else None


def _decode_and_get_session(cur, session_id: str) -> dict:
    """Decode hash and get session."""
    info(f"Attempting to decode hash: {session_id}")
    hash_type = detect_hash_type(session_id)
    info(f"Detected hash type: {hash_type}")

    decoded_id = None

    # Try session decoder first
    decoded_id = decode_session_code(session_id)
    if decoded_id:
        info(f"Successfully decoded as session hash: {session_id} -> {decoded_id}")
    else:
        # If detected as assessment, try assessment decoder
        if hash_type == "assessment":
            decoded_id = decode_assessment_id(session_id)
            if decoded_id:
                info(f"Successfully decoded as assessment hash (treating as session): {session_id} -> {decoded_id}")

    info(f"Final decode result: {decoded_id}")

    if decoded_id:
        info(f"Successfully decoded {session_id} as session hash to ID: {decoded_id}")
        query = _get_session_query() + "WHERE s.id = %s"
        cur.execute(query, (decoded_id,))
        session = cur.fetchone()
        if session:
            session_dict = dict(session)
            # Convert datetime objects to ISO strings
            for key, value in session_dict.items():
                if hasattr(value, "isoformat"):
                    session_dict[key] = value.isoformat()
            return session_dict
    else:
        warning(f"Invalid session hash format: {session_id}")
        raise HTTPException(status_code=400, detail=f"Invalid session hash: {session_id}")

    return None


def _generate_session_error_message(session_id: str) -> str:
    """Generate appropriate error message based on session_id format."""
    if len(session_id) == 6 and session_id.isdigit():
        return f"Session with 6-digit code {session_id} not found"
    elif session_id.isdigit():
        return f"Session with numeric ID {session_id} not found"
    else:
        return f"Session with ID {session_id} not found. Expected: numeric ID or 6-digit session code"


def _enhance_completed_session_data(session_dict: dict) -> dict:
    """Add score and performance data for completed sessions."""
    if session_dict.get("status") == "completed" and session_dict.get("score") is not None:
        obtained_score = float(session_dict["score"])
        assessment_id = session_dict["assessment_id"]
        session_internal_id = session_dict["id"]

        # Calculate correct total possible score
        total_possible_score = calculate_total_score_for_assessment(assessment_id, session_internal_id)

        # Calculate performance level
        performance_level = get_performance_level_with_correct_total(obtained_score, assessment_id, session_internal_id)

        # Calculate percentage
        percentage = (obtained_score / total_possible_score * 100) if total_possible_score > 0 else 0

        # Add to session dict
        session_dict["obtained_score"] = obtained_score
        session_dict["total_possible_score"] = total_possible_score
        session_dict["percentage"] = round(percentage, 2)
        session_dict["performance_level"] = performance_level

    return session_dict


def _prepare_session_response(session_dict: dict) -> dict:
    """Prepare session dictionary for response."""
    if session_dict and "id_hash" not in session_dict:
        # Enhance completed session data
        session_dict = _enhance_completed_session_data(session_dict)

        # Convert datetime objects to ISO strings before hash transformation
        for key, value in session_dict.items():
            if hasattr(value, "isoformat"):
                session_dict[key] = value.isoformat()

        session_dict = hash_ids_in_response(session_dict)

    return session_dict


@api_router.get("/admin/sessions/{session_id}/details")
async def get_session_details_endpoint(session_id: str):
    """Get detailed session information by session ID (numeric ID or 6-digit session code)"""
    try:
        info(f"Session detail request for ID: {session_id} (type: {type(session_id)}, length: {len(session_id)})")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                session_dict = None

                # Check if it's a 6-digit session code first
                if len(session_id) == 6 and session_id.isdigit():
                    session_dict = _get_session_by_code(cur, session_id)
                # If it's a numeric ID (but not 6 digits), get directly by ID
                elif session_id.isdigit():
                    session_dict = _get_session_by_numeric_id(cur, session_id)
                else:
                    # Try to decode hash
                    session_dict = _decode_and_get_session(cur, session_id)

                if not session_dict:
                    error_detail = _generate_session_error_message(session_id)
                    warning(f"Session lookup failed: {error_detail}")
                    raise HTTPException(status_code=404, detail=error_detail)

                # Prepare session response
                session_dict = _prepare_session_response(session_dict)

                info(f"Returning session data keys: {list(session_dict.keys()) if session_dict else None}")

                return success_response(data=session_dict, message="Session details retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting session details for ID {session_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error getting session details: {str(e)}")


@api_router.get("/admin/sessions/{session_id}/results")
async def get_session_results(session_id: str):
    """Get detailed session results including questions, answers, and score breakdown"""
    try:
        info(f"Session results request for ID: {session_id}")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                session_dict = None

                # Check if it's a 6-digit session code first
                if len(session_id) == 6 and session_id.isdigit():
                    info(f"Using 6-digit session code: {session_id}")

                    cur.execute(
                        """
                        SELECT s.id, s.code as session_code, u.external_id as username,
                               s.assessment_id, s.score, s.status,
                               s.created_at, s.completed_at, s.started_at,
                               a.name as assessment_name, a.question_selection_mode,
                               CASE
                                   WHEN a.question_selection_mode = 'fixed' THEN
                                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                                   ELSE
                                       (SELECT COUNT(DISTINCT ua.question_id)
                                        FROM user_answers ua
                                        WHERE ua.session_id = s.id)
                               END as total_questions
                        FROM sessions s
                        JOIN assessments a ON s.assessment_id = a.id
                        JOIN users u ON s.user_id = u.id
                        WHERE s.code = %s AND s.status = 'completed'
                        """,
                        (session_id,),
                    )
                    session_dict = cur.fetchone()

                # If it's a numeric ID, get directly by ID
                elif session_id.isdigit():
                    actual_id = int(session_id)
                    info(f"Using numeric session ID: {actual_id}")

                    cur.execute(
                        """
                        SELECT s.id, s.code as session_code, u.external_id as username,
                               s.assessment_id, s.score, s.status,
                               s.created_at, s.completed_at, s.started_at,
                               a.name as assessment_name, a.question_selection_mode,
                               CASE
                                   WHEN a.question_selection_mode = 'fixed' THEN
                                       (SELECT COUNT(*) FROM assessment_questions aq WHERE aq.assessment_id = a.id)
                                   ELSE
                                       (SELECT COUNT(DISTINCT ua.question_id)
                                        FROM user_answers ua
                                        WHERE ua.session_id = s.id)
                               END as total_questions
                        FROM sessions s
                        JOIN assessments a ON s.assessment_id = a.id
                        JOIN users u ON s.user_id = u.id
                        WHERE s.id = %s AND s.status = 'completed'
                        """,
                        (actual_id,),
                    )
                    session_dict = cur.fetchone()

                if not session_dict:
                    return error_response(
                        message="Completed session not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                session_dict = dict(session_dict)
                session_internal_id = session_dict["id"]

                # Get detailed questions and answers for this session
                cur.execute(
                    """
                    SELECT ua.question_id, ua.user_answer, ua.is_correct, ua.score,
                           q.question, q.options, q.answer as correct_answer_key, q.level
                    FROM user_answers ua
                    JOIN questions q ON ua.question_id = q.que_id
                    WHERE ua.session_id = %s
                    ORDER BY ua.created_at
                    """,
                    (session_internal_id,),
                )
                answers = cur.fetchall()

                # Format questions and answers for frontend
                answered_questions = []
                correct_answers = 0
                total_score = 0

                for answer in answers:
                    answer_dict = dict(answer)

                    # Parse options if it's a JSON string
                    options = answer_dict["options"]
                    if isinstance(options, str):
                        try:
                            import json

                            options = json.loads(options)
                        except (json.JSONDecodeError, TypeError):
                            options = {}

                    question_data = {
                        "question": answer_dict["question"],
                        "options": options,
                        "userAnswer": answer_dict["user_answer"],
                        "correctAnswerKey": answer_dict["correct_answer_key"],
                        "isCorrect": answer_dict["is_correct"],
                        "score": float(answer_dict["score"]) if answer_dict["score"] else 0,
                        "level": answer_dict["level"],
                    }

                    answered_questions.append(question_data)

                    if answer_dict["is_correct"]:
                        correct_answers += 1

                    total_score += question_data["score"]

                # Prepare response data
                result_data = {
                    "session_info": {
                        "session_code": session_dict["session_code"],
                        "username": session_dict["username"],
                        "assessment_name": session_dict["assessment_name"],
                        "completed_at": (
                            session_dict["completed_at"].isoformat() if session_dict["completed_at"] else None
                        ),
                        "question_selection_mode": session_dict["question_selection_mode"],
                    },
                    "score_summary": {
                        "correct_answers": correct_answers,
                        "total_questions": len(answered_questions),
                        "questions_attempted": len(answered_questions),
                        "total_score": round(total_score, 1),
                        "final_score": float(session_dict["score"]) if session_dict["score"] else total_score,
                    },
                    "answered_questions": answered_questions,
                }

                return success_response(data=result_data, message="Session results retrieved successfully")

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting session results for ID {session_id}: {str(e)}")
        return error_response(
            message=f"Error getting session results: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/admin/sessions/{session_code}/user")
async def get_session_user(session_code: str):
    """Get the username associated with a session code"""
    try:
        if not session_code or len(session_code) != 6 or not session_code.isdigit():
            raise_http_exception(status_code=400, detail="Session code must be a 6-digit number")

        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # First, check if the session exists
                cur.execute(
                    """
                    SELECT s.id, s.user_id, s.assessment_id, s.status
                    FROM sessions s
                    WHERE s.code = %s
                    """,
                    (session_code,),
                )
                session_result = cur.fetchone()

                if not session_result:
                    warning(f"No session found for code: {session_code}")
                    raise_http_exception(status_code=404, detail="Session code not found")

                # Now get the user details (excluding sensitive data)
                cur.execute(
                    """
                    SELECT u.external_id, u.display_name
                    FROM users u
                    WHERE u.id = %s
                    """,
                    (session_result["user_id"],),
                )
                user_result = cur.fetchone()

                if not user_result:
                    warning(f"No user found for user_id: {session_result['user_id']}")
                    raise_http_exception(status_code=404, detail="User not found for this session")

                # Get the assessment details
                cur.execute(
                    """
                    SELECT a.name as assessment_name, a.is_final
                    FROM assessments a
                    WHERE a.id = %s
                    """,
                    (session_result["assessment_id"],),
                )
                assessment_result = cur.fetchone()

                if not assessment_result:
                    warning(f"No assessment found for assessment_id: {session_result['assessment_id']}")
                    raise_http_exception(status_code=404, detail="Assessment not found for this session")

                # Use display_name if available, otherwise fall back to external_id
                username = user_result["display_name"] if user_result["display_name"] else user_result["external_id"]

                response_data = {
                    "username": username,
                    "assessment_id": session_result["assessment_id"],
                    "assessment_name": assessment_result["assessment_name"],
                    "is_final": assessment_result["is_final"],
                    "session_status": session_result["status"],
                }
                return hash_ids_in_response(response_data)

    except HTTPException:
        raise
    except Exception as e:
        error(f"Error fetching session user for code {session_code}: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error fetching session user: {str(e)}")


@api_router.get("/admin/assessments-with-sessions")
async def get_assessments_with_sessions():
    """Get only assessments that have existing sessions"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT DISTINCT a.id, a.name, a.description, a.is_final,
                           a.total_questions, a.question_selection_mode,
                           a.composition, a.created_at,
                           COUNT(s.id) as session_count
                    FROM assessments a
                    INNER JOIN sessions s ON a.id = s.assessment_id
                    GROUP BY a.id, a.name, a.description, a.is_final,
                             a.total_questions, a.question_selection_mode,
                             a.composition, a.created_at
                    ORDER BY a.created_at DESC
                    """
                )
                assessments = [dict(row) for row in cur.fetchall()]
                hashed_assessments = hash_ids_in_response(assessments)

                return success_response(
                    data={"assessments": hashed_assessments},
                    message="Assessments with sessions retrieved successfully",
                )

    except Exception as e:
        error(f"Error fetching assessments with sessions: {str(e)}")
        raise_http_exception(status_code=500, detail=f"Error fetching assessments with sessions: {str(e)}")


@api_router.get("/admin/users")
async def get_all_users():
    """Get all users from the database"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    """
                    SELECT
                        u.id,
                        u.external_id,
                        u.display_name,
                        u.created_at,
                        u.email,
                        COUNT(DISTINCT s.id) as session_count
                    FROM users u
                    LEFT JOIN sessions s ON u.id = s.user_id
                    GROUP BY u.id, u.external_id, u.email, u.display_name, u.created_at
                    ORDER BY u.created_at DESC
                    """
                )
                users = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for user in users:
                    if user["created_at"]:
                        user["created_at"] = user["created_at"].isoformat()
                    # Use external_id as display name if display_name is None
                    if not user["display_name"]:
                        user["display_name"] = user["external_id"]

                return success_response(
                    data={"users": users},
                    message="Users retrieved successfully",
                )
    except Exception as e:
        error(f"Error getting users: {str(e)}")
        return error_response(
            message=f"Error getting users: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/admin/users/{user_id}/assessments")
async def get_user_assessments(user_id: int):
    """Get all assessments taken by a specific user with their scores"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (excluding sensitive data)
                cur.execute(
                    """
                    SELECT id, external_id, display_name
                    FROM users
                    WHERE id = %s
                    """,
                    (user_id,),
                )
                user = cur.fetchone()

                if not user:
                    return error_response(
                        message=f"User with ID {user_id} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = user_dict["external_id"]

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session["session_created"].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session["session_completed"].isoformat()

                response_data = {"user": user_dict, "assessments": sessions}
                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message="User assessments retrieved successfully",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments: {str(e)}")
        return error_response(
            message=f"Error getting user assessments: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


@api_router.get("/user/{email}/assessments")
async def get_user_assessments_by_email(email: str):
    """Get all assessments taken by a specific user identified by email"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                # Get user details (excluding sensitive data from response)
                cur.execute(
                    """
                    SELECT id, external_id, display_name
                    FROM users
                    WHERE email = %s
                    """,
                    (email,),
                )
                user = cur.fetchone()

                if not user:
                    return error_response(
                        message=f"User with email {email} not found",
                        code=status.HTTP_404_NOT_FOUND,
                        error_type="NotFound",
                    )

                user_dict = dict(user)
                if not user_dict["display_name"]:
                    user_dict["display_name"] = user_dict["external_id"]

                user_id = user_dict["id"]

                # Get all sessions for this user with assessment details and scores
                cur.execute(
                    """
                    SELECT
                        s.id AS session_id,
                        s.created_at AS session_created,
                        s.completed_at AS session_completed,
                        a.id AS assessment_id,
                        a.name AS assessment_name,
                        a.question_selection_mode AS mode,
                        a.description AS assessment_description,
                        s.score,
                        s.status,

                        COALESCE(SUM(CASE WHEN q.level = 'easy' THEN 1 ELSE 0 END), 0) AS easy_count,
                        COALESCE(SUM(CASE WHEN q.level = 'intermediate' THEN 1 ELSE 0 END), 0) AS intermediate_count,
                        COALESCE(SUM(CASE WHEN q.level = 'advanced' THEN 1 ELSE 0 END), 0) AS advanced_count

                    FROM sessions s
                    JOIN assessments a ON s.assessment_id = a.id
                    LEFT JOIN assessment_questions aq ON a.id = aq.assessment_id
                    LEFT JOIN questions q ON aq.question_id = q.que_id

                    WHERE s.user_id = %s

                    GROUP BY
                        s.id, s.created_at, s.completed_at, s.score, s.status,
                        a.id, a.name, a.question_selection_mode, a.description

                    ORDER BY
                        (s.status = 'completed') DESC,
                        s.created_at DESC;

                    """,
                    (user_id,),
                )
                sessions = [dict(row) for row in cur.fetchall()]

                # Convert datetime objects to ISO format strings
                for session in sessions:
                    if session["session_created"]:
                        session["session_created"] = session["session_created"].isoformat()
                    if session["session_completed"]:
                        session["session_completed"] = session["session_completed"].isoformat()

                response_data = {"user": user_dict, "assessments": sessions}
                hashed_data = hash_ids_in_response(response_data)
                return success_response(
                    data=hashed_data,
                    message=f"User assessments retrieved successfully for {email}",
                )
    except HTTPException:
        raise
    except Exception as e:
        error(f"Error getting user assessments by email: {str(e)}")
        return error_response(
            message=f"Error getting user assessments by email: {str(e)}",
            code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            error_type="InternalServerError",
        )


# ============================================================================
# WORKER OPERATIONS
# ============================================================================
# Worker service now only handles Dapr pubsub messages internally.
# All worker-related HTTP endpoints have been removed as they are not needed.
# The worker service uses Dapr for all operational data and only touches
# PostgreSQL for storing actual questions (business data).


# Include skill router in the API router
api_router.include_router(skill_router)

# Include API router with all endpoints - this must be done after all routes are defined
app.include_router(api_router)
info("API router setup complete - all routes registered")


if __name__ == "__main__":
    port = int(os.getenv("SERVER_PORT", "8000"))

    # Configure uvicorn to use our centralized logging
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=port,
        access_log=False,  # Disable uvicorn's access logging
        log_config=None,  # Use our logging configuration
    )
